/**
 * ChatAI Widget - Embeddable Chat Interface
 * Easy integration: Just include this script and call ChatAIWidget.init()
 */

class ChatAIWidget {
    constructor(options = {}) {
        this.options = {
            chatUrl: options.chatUrl || './streaming-ui.html',
            position: options.position || 'bottom-right', // bottom-right, bottom-left, top-right, top-left
            theme: options.theme || 'default',
            showNotification: options.showNotification || false,
            buttonText: options.buttonText || '💬',
            closeText: options.closeText || '×',
            ...options
        };
        
        this.isChatOpen = false;
        this.elements = {};
        
        this.init();
    }

    init() {
        this.createStyles();
        this.createWidget();
        this.attachEventListeners();
    }

    createStyles() {
        const styles = `
            .chatai-widget {
                position: fixed;
                ${this.getPositionStyles()}
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            }

            .chatai-widget-button {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                cursor: pointer;
                box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                color: white;
                font-size: 24px;
                outline: none;
            }

            .chatai-widget-button:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
            }

            .chatai-widget-button.active {
                background: #dc3545;
            }

            .chatai-widget-button.active:hover {
                box-shadow: 0 6px 25px rgba(220, 53, 69, 0.6);
            }

            .chatai-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 9999;
                display: none;
                align-items: center;
                justify-content: center;
                padding: 20px;
                box-sizing: border-box;
            }

            .chatai-overlay.active {
                display: flex;
            }

            .chatai-iframe-container {
                width: 100%;
                max-width: 900px;
                height: 93vh;
                background: white;
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                position: relative;
            }

            .chatai-iframe {
                width: 100%;
                height: 100%;
                border: none;
                border-radius: 16px;
            }

            .chatai-close-button {
                position: absolute;
                top: 15px;
                right: 15px;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: rgba(0, 0, 0, 0.1);
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
                font-size: 18px;
                z-index: 10001;
                transition: all 0.2s ease;
                outline: none;
            }

            .chatai-close-button:hover {
                background: rgba(0, 0, 0, 0.2);
                color: #333;
            }

            .chatai-notification {
                position: absolute;
                top: -5px;
                right: -5px;
                width: 20px;
                height: 20px;
                background: #dc3545;
                border-radius: 50%;
                display: none;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 12px;
                font-weight: bold;
                animation: chatai-pulse 2s infinite;
            }

            @keyframes chatai-pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }

            @media (max-width: 768px) {
                .chatai-iframe-container {
                    width: 100%;
                    height: 100vh;
                    border-radius: 0;
                    max-width: none;
                }

                .chatai-iframe {
                    border-radius: 0;
                }

                .chatai-overlay {
                    padding: 0;
                }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    getPositionStyles() {
        const positions = {
            'bottom-right': 'bottom: 20px; right: 20px;',
            'bottom-left': 'bottom: 20px; left: 20px;',
            'top-right': 'top: 20px; right: 20px;',
            'top-left': 'top: 20px; left: 20px;'
        };
        return positions[this.options.position] || positions['bottom-right'];
    }

    createWidget() {
        // Create widget container
        const widget = document.createElement('div');
        widget.className = 'chatai-widget';
        widget.innerHTML = `
            <button class="chatai-widget-button" id="chatai-button">
                <span id="chatai-icon">${this.options.buttonText}</span>
            </button>
            ${this.options.showNotification ? '<div class="chatai-notification" id="chatai-notification">1</div>' : ''}
        `;

        // Create overlay
        const overlay = document.createElement('div');
        overlay.className = 'chatai-overlay';
        overlay.id = 'chatai-overlay';
        overlay.innerHTML = `
            <div class="chatai-iframe-container">
                <button class="chatai-close-button" id="chatai-close-button">${this.options.closeText}</button>
                <iframe class="chatai-iframe" id="chatai-iframe" src=""></iframe>
            </div>
        `;

        // Append to body
        document.body.appendChild(widget);
        document.body.appendChild(overlay);

        // Store element references
        this.elements = {
            button: document.getElementById('chatai-button'),
            overlay: document.getElementById('chatai-overlay'),
            closeButton: document.getElementById('chatai-close-button'),
            iframe: document.getElementById('chatai-iframe'),
            icon: document.getElementById('chatai-icon'),
            notification: document.getElementById('chatai-notification')
        };
    }

    attachEventListeners() {
        this.elements.button.addEventListener('click', () => this.toggleChat());
        this.elements.closeButton.addEventListener('click', () => this.closeChat());
        
        // Close when clicking outside
        this.elements.overlay.addEventListener('click', (e) => {
            if (e.target === this.elements.overlay) {
                this.closeChat();
            }
        });

        // Close with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isChatOpen) {
                this.closeChat();
            }
        });
    }

    openChat() {
        if (!this.isChatOpen) {
            this.elements.iframe.src = this.options.chatUrl;
            this.elements.overlay.classList.add('active');
            this.elements.button.classList.add('active');
            this.elements.icon.textContent = this.options.closeText;
            this.isChatOpen = true;
            
            // Hide notification
            if (this.elements.notification) {
                this.elements.notification.style.display = 'none';
            }
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }
    }

    closeChat() {
        if (this.isChatOpen) {
            this.elements.overlay.classList.remove('active');
            this.elements.button.classList.remove('active');
            this.elements.icon.textContent = this.options.buttonText;
            this.isChatOpen = false;
            
            // Re-enable body scroll
            document.body.style.overflow = '';
            
            // Clear iframe after animation
            setTimeout(() => {
                if (!this.isChatOpen) {
                    this.elements.iframe.src = '';
                }
            }, 300);
        }
    }

    toggleChat() {
        if (this.isChatOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    showNotification(count = 1) {
        if (this.elements.notification) {
            this.elements.notification.textContent = count;
            this.elements.notification.style.display = 'flex';
        }
    }

    hideNotification() {
        if (this.elements.notification) {
            this.elements.notification.style.display = 'none';
        }
    }

    // Static method for easy initialization
    static init(options = {}) {
        return new ChatAIWidget(options);
    }
}

// Auto-initialize if script is loaded with data attributes
document.addEventListener('DOMContentLoaded', () => {
    const script = document.querySelector('script[src*="chatai-widget"]');
    if (script && script.hasAttribute('data-auto-init')) {
        const options = {};
        
        // Read options from data attributes
        if (script.hasAttribute('data-chat-url')) {
            options.chatUrl = script.getAttribute('data-chat-url');
        }
        if (script.hasAttribute('data-position')) {
            options.position = script.getAttribute('data-position');
        }
        if (script.hasAttribute('data-show-notification')) {
            options.showNotification = script.getAttribute('data-show-notification') === 'true';
        }
        
        ChatAIWidget.init(options);
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatAIWidget;
}

// Global access
window.ChatAIWidget = ChatAIWidget;
