# RAG System Documentation

## 📚 Complete Documentation Suite

Welcome to the comprehensive documentation for the RAG (Retrieval-Augmented Generation) system. This documentation provides everything you need to understand, deploy, and maintain the system.

## 📋 Documentation Index

### 🏗️ [RAG System Complete Documentation](./RAG_SYSTEM_DOCUMENTATION.md)
**Main technical overview and system architecture**
- System overview and key features
- Architecture components and service details
- Complete data flow processes (upload & query)
- Configuration and troubleshooting guides

### 🔌 [API Reference](./API_REFERENCE.md)
**Comprehensive API documentation**
- All endpoint specifications with examples
- Request/response formats and authentication
- Error handling and rate limiting
- SDK integration examples (JavaScript, Python, cURL)

### 🚀 [Deployment Guide](./DEPLOYMENT_GUIDE.md)
**Step-by-step deployment instructions**
- Local development setup
- Docker deployment configurations
- Production deployment with security
- Monitoring, backup, and maintenance procedures

### 🏛️ [Architecture Deep Dive](./ARCHITECTURE_DEEP_DIVE.md)
**In-depth technical architecture analysis**
- Microservices design patterns
- Performance optimizations and caching strategies
- Scalability considerations and security architecture
- Future enhancement roadmap

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+ (for local development)
- 8GB+ RAM, 20GB+ storage

### 1. Clone & Setup
```bash
git clone <repository-url>
cd RAG/under_construction
```

### 2. Start Infrastructure
```bash
docker-compose up -d postgres qdrant rabbitmq
```

### 3. Configure Environment
Create `.env` files for both services (see [Deployment Guide](./DEPLOYMENT_GUIDE.md) for details)

### 4. Start Services
```bash
# User-Service (Terminal 1)
cd User-Service && npm install && npm run start:dev

# ChatAI-SDK-Clean (Terminal 2)
cd ChatAI-SDK-Clean && npm install && npm run dev
```

### 5. Verify Installation
```bash
curl http://localhost:3000/health  # User-Service
curl http://localhost:3001/health  # ChatAI-SDK-Clean
```

## 🏗️ System Architecture Overview

```mermaid
graph TB
    Client[🌐 Client] --> ChatAI[🤖 ChatAI-SDK-Clean<br/>Port: 3001]
    Client --> UserService[👤 User-Service<br/>Port: 3000]
    
    ChatAI --> UserService
    ChatAI --> Qdrant[(🔍 Qdrant Vector DB<br/>Port: 6333)]
    UserService --> PostgreSQL[(🐘 PostgreSQL<br/>Port: 5433)]
    
    ChatAI --> LlamaIndex[📄 LlamaIndex Cloud]
    ChatAI --> SambaNova[🧠 SambaNova API]
    ChatAI --> OpenRouter[💬 OpenRouter API]
    
    UserService --> RabbitMQ[🐰 RabbitMQ<br/>Port: 5672]
```

## 🔄 Core Workflows

### Document Upload Flow
1. **Upload** → User-Service validates and stores metadata
2. **Process** → ChatAI-SDK-Clean handles parsing and vectorization
3. **Parse** → LlamaIndex extracts text and structure
4. **Embed** → SambaNova generates 1024D vectors
5. **Store** → Qdrant indexes vectors with tenant isolation
6. **Ready** → Document available for querying

### Query Processing Flow
1. **Query** → Client sends question to ChatAI-SDK-Clean
2. **Validate** → API key verification with User-Service
3. **Vectorize** → Query converted to embedding
4. **Search** → Qdrant finds similar document chunks
5. **Generate** → OpenRouter creates contextual response
6. **Stream** → Real-time response delivery to client

## 📊 Key Features

### 🔒 **Security & Isolation**
- Multi-tenant architecture with AppId-based isolation
- JWT authentication and API key validation
- Encrypted data storage and secure communications

### ⚡ **Performance & Scalability**
- Streaming responses for real-time user experience
- Batch processing with retry mechanisms
- Multi-level caching (API keys, embeddings, sessions)
- Horizontal scaling support

### 🧠 **Intelligent Processing**
- Advanced document chunking (page-based + semantic)
- Junk filtering to improve search quality
- Quality-based context formation
- Out-of-context query handling

### 🔧 **Developer Experience**
- Comprehensive API documentation
- Health check endpoints
- Detailed error responses
- Multiple SDK examples

## 🛠️ Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Backend** | Node.js (NestJS, Express) | Service implementation |
| **Database** | PostgreSQL | User data, metadata |
| **Vector DB** | Qdrant | Document embeddings |
| **Message Queue** | RabbitMQ | Async processing |
| **Parsing** | LlamaIndex Cloud | Document text extraction |
| **Embeddings** | SambaNova API | Vector generation |
| **LLM** | OpenRouter API | Response generation |
| **Containerization** | Docker Compose | Service orchestration |

## 📈 Performance Metrics

### Processing Capabilities
- **File Size**: Up to 20MB per document
- **Formats**: PDF, DOCX, TXT, PPTX
- **Embedding Dimensions**: 1024D vectors
- **Search Latency**: <200ms for vector search
- **Response Time**: <2s for complete query processing

### Rate Limits
- **General API**: 100 requests per 15 minutes
- **SambaNova**: 30 RPM, 150 RPH, 1800 RPD
- **Batch Processing**: 3 chunks per embedding request

## 🔍 API Endpoints

### Main Query Endpoint
```http
GET /api/v1/?apikey=<key>&query=<question>&stream=true
```

### Document Upload
```http
POST /users/app/chatai/upload-document
Content-Type: multipart/form-data
Authorization: Bearer <token>
```

### Health Checks
```http
GET /health  # Both services
```

## 🐛 Troubleshooting

### Common Issues
- **Service Connection**: Check Docker containers and ports
- **Vector Search**: Verify Qdrant collection and embeddings
- **API Keys**: Validate external service credentials
- **Processing Failures**: Check logs for parsing/embedding errors

### Debug Commands
```bash
# Service status
docker-compose ps

# Service logs
docker-compose logs -f service-name

# Health checks
curl http://localhost:3001/health

# Database verification
docker exec -it postgres psql -U postgres -d abstraxn
```

## 📞 Support & Maintenance

### Monitoring
- Health check endpoints for all services
- Comprehensive logging with structured formats
- Performance metrics tracking
- Error rate monitoring

### Backup & Recovery
- Automated PostgreSQL backups
- Qdrant snapshot management
- Environment configuration backup
- Disaster recovery procedures

## 🔄 Version Information

- **Current Version**: 1.0.0
- **Last Updated**: 2024-01-01
- **Node.js**: 18+
- **Docker**: 20.10+
- **Documentation**: Complete and up-to-date

## 📚 Additional Resources

- **[System Architecture Diagram](./RAG_SYSTEM_DOCUMENTATION.md#architecture-components)**
- **[Complete API Specifications](./API_REFERENCE.md)**
- **[Production Deployment Guide](./DEPLOYMENT_GUIDE.md#production-deployment)**
- **[Performance Optimization](./ARCHITECTURE_DEEP_DIVE.md#performance-optimizations)**

---

**Need Help?** 
- Check the troubleshooting sections in each document
- Review service logs for error details
- Verify environment configuration
- Test individual components with health checks

This documentation suite provides complete coverage of the RAG system from basic setup to advanced architecture concepts. Each document is designed to be self-contained while cross-referencing related information for comprehensive understanding.
