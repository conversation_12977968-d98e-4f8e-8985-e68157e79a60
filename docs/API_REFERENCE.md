# RAG System API Reference

## Overview

This document provides detailed API specifications for the RAG system's endpoints, including request/response formats, authentication, and error handling.

## Base URLs

- **User-Service**: `http://localhost:3000`
- **ChatAI-SDK-Clean**: `http://localhost:3001`

## Authentication

### API Key Authentication
Most endpoints require API key authentication via query parameter:
```
?apikey=your_api_key_here
```

### Bearer Token Authentication
Some User-Service endpoints require JWT bearer tokens:
```
Authorization: Bearer <jwt_token>
```

## User-Service API

### Document Management

#### Upload Document
Upload a document for processing.

**Endpoint**: `POST /users/app/chatai/upload-document`

**Authentication**: Bearer Token

**Content-Type**: `multipart/form-data`

**Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file` | File | Yes | Document file (max 20MB) |
| `appId` | String | Yes | Application ID |
| `filename` | String | No | Override filename |

**Request Example**:
```bash
curl -X POST http://localhost:3000/users/app/chatai/upload-document \
  -H "Authorization: Bearer <token>" \
  -F "file=@document.pdf" \
  -F "appId=app_123" \
  -F "filename=my_document.pdf"
```

**Response**:
```json
{
  "success": true,
  "message": "Document uploaded successfully",
  "data": {
    "documentId": "550e8400-e29b-41d4-a716-************",
    "filename": "my_document.pdf",
    "status": "parsing",
    "appId": "app_123",
    "uploadedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Get Documents
Retrieve user's documents.

**Endpoint**: `GET /users/app/chatai/documents`

**Authentication**: Bearer Token

**Query Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `appId` | String | Yes | Application ID |
| `status` | String | No | Filter by status |
| `limit` | Number | No | Results limit (default: 10) |
| `offset` | Number | No | Results offset (default: 0) |

**Response**:
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "filename": "document.pdf",
        "status": "ready_for_chat",
        "uploadedAt": "2024-01-01T00:00:00.000Z",
        "processedAt": "2024-01-01T00:05:00.000Z",
        "metadata": {
          "pageCount": 10,
          "wordCount": 2500,
          "fileSize": 1048576
        }
      }
    ],
    "total": 1,
    "limit": 10,
    "offset": 0
  }
}
```

#### Remove Document
Delete a document and its embeddings.

**Endpoint**: `DELETE /users/app/chatai/remove-document`

**Authentication**: Bearer Token

**Request Body**:
```json
{
  "documentId": "550e8400-e29b-41d4-a716-************",
  "appId": "app_123"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Document removed successfully",
  "data": {
    "documentId": "550e8400-e29b-41d4-a716-************",
    "removedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### API Key Validation

#### Validate API Key
Internal endpoint for API key validation.

**Endpoint**: `POST /users/app/key-validator`

**Authentication**: Internal API Key

**Request Body**:
```json
{
  "apiKey": "test_api_key_1751884336144_vp9gospvg",
  "origin": "http://localhost:3001"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>"
    },
    "application": {
      "id": "app_123",
      "name": "My App",
      "apiKey": "test_api_key_1751884336144_vp9gospvg"
    },
    "documents": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "filename": "document.pdf",
        "status": "ready_for_chat",
        "parsedData": {
          "chunks": [
            {
              "page": 1,
              "text": "Document content..."
            }
          ]
        }
      }
    ]
  }
}
```

## ChatAI-SDK-Clean API

### Query Processing

#### Main Query Endpoint
Process user queries with document context.

**Endpoint**: `GET /api/v1/`

**Authentication**: API Key (query parameter)

**Query Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `apikey` | String | Yes | Application API key |
| `query` | String | Yes | User question |
| `stream` | Boolean | No | Enable streaming (default: true) |
| `sessionId` | String | No | Session identifier |
| `testMode` | Boolean | No | Bypass user service (default: false) |

**Request Example**:
```bash
curl "http://localhost:3001/api/v1/?apikey=test_api_key_1751884336144_vp9gospvg&query=What%20is%20the%20invoice%20amount&stream=true"
```

**Streaming Response** (Server-Sent Events):
```
data: {"type":"content","content":"Based"}
data: {"type":"content","content":" on"}
data: {"type":"content","content":" the"}
data: {"type":"content","content":" document"}
data: {"type":"done","timestamp":"2024-01-01T00:00:00.000Z","timing":{"total":1500}}
```

**Non-Streaming Response**:
```json
{
  "response": "Based on the document, the invoice amount is $1,250.00.",
  "metadata": {
    "documentsUsed": 2,
    "contextLength": 1024,
    "timing": {
      "total": 1500,
      "userService": 100,
      "vectorSearch": 200,
      "llmGeneration": 800
    },
    "cached": {
      "apiKey": false,
      "context": false
    }
  }
}
```

### Document Processing

#### Complete Document Processing
Internal endpoint for complete document processing.

**Endpoint**: `POST /upload-and-process`

**Authentication**: Internal

**Content-Type**: `multipart/form-data`

**Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file` | File | Yes | Document file buffer |
| `appId` | String | Yes | Application ID |
| `documentId` | String | Yes | Document UUID |
| `userId` | String | No | User ID |
| `filename` | String | No | Document filename |

**Response**:
```json
{
  "success": true,
  "message": "Document uploaded, parsed, and processed for vector search",
  "data": {
    "documentId": "550e8400-e29b-41d4-a716-************",
    "appId": "app_123",
    "filename": "document.pdf",
    "parsing": {
      "jobId": "parse_job_123",
      "textLength": 15000,
      "pageCount": 10,
      "wordCount": 2500
    },
    "vectorProcessing": {
      "totalChunks": 25,
      "storedChunks": 23,
      "chunkingStrategy": "page_based",
      "pageBasedChunks": 20,
      "status": "ready_for_chat"
    },
    "status": "ready_for_chat"
  }
}
```

### Health Check

#### System Health
Check system and component health.

**Endpoint**: `GET /health`

**Authentication**: None

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "ChatAI SDK Clean with Qdrant Vector Database",
  "endpoints": {
    "main": "/api/v1/?apikey=...&query=...",
    "health": "/health"
  },
  "vectorDatabase": {
    "status": "healthy",
    "stats": {
      "collections": 1,
      "points": 1250,
      "indexedVectors": 1250,
      "ramUsage": "45.2 MB",
      "diskUsage": "128.5 MB"
    }
  }
}
```

## Error Responses

### Standard Error Format
All APIs return errors in a consistent format:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "Additional error details"
  }
}
```

### Common Error Codes

#### Authentication Errors
- **401 Unauthorized**: Invalid or missing API key
- **403 Forbidden**: API key valid but access denied

#### Validation Errors
- **400 Bad Request**: Invalid request parameters
- **413 Payload Too Large**: File size exceeds 20MB limit

#### Processing Errors
- **422 Unprocessable Entity**: Document parsing failed
- **429 Too Many Requests**: Rate limit exceeded

#### Server Errors
- **500 Internal Server Error**: Unexpected server error
- **502 Bad Gateway**: External service unavailable
- **503 Service Unavailable**: Service temporarily unavailable

### Error Examples

#### Invalid API Key
```json
{
  "success": false,
  "error": "Invalid API key",
  "code": "INVALID_API_KEY",
  "details": {
    "apiKey": "test_invalid_key"
  }
}
```

#### File Too Large
```json
{
  "success": false,
  "error": "File size exceeds maximum limit",
  "code": "FILE_TOO_LARGE",
  "details": {
    "maxSize": "20MB",
    "actualSize": "25MB"
  }
}
```

#### Rate Limit Exceeded
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "code": "RATE_LIMIT_EXCEEDED",
  "details": {
    "limit": 100,
    "window": "15 minutes",
    "retryAfter": 300
  }
}
```

## Rate Limiting

### General Rate Limits
- **Window**: 15 minutes
- **Requests**: 100 per window
- **Headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

### Chat-Specific Limits
- **Endpoint**: `/api/v1/`
- **Stricter limits** applied for query processing

### External API Limits
- **SambaNova**: 30 RPM, 150 RPH, 1800 RPD
- **LlamaIndex**: No specific limits documented
- **OpenRouter**: Varies by model and plan

## SDK Integration Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

// Upload document
const uploadDocument = async (file, apiKey, appId) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('appId', appId);
  
  const response = await axios.post(
    'http://localhost:3000/users/app/chatai/upload-document',
    formData,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data'
      }
    }
  );
  
  return response.data;
};

// Query documents
const queryDocuments = async (query, apiKey) => {
  const response = await axios.get(
    `http://localhost:3001/api/v1/?apikey=${apiKey}&query=${encodeURIComponent(query)}&stream=false`
  );
  
  return response.data;
};
```

### Python
```python
import requests

# Upload document
def upload_document(file_path, token, app_id):
    with open(file_path, 'rb') as file:
        files = {'file': file}
        data = {'appId': app_id}
        headers = {'Authorization': f'Bearer {token}'}
        
        response = requests.post(
            'http://localhost:3000/users/app/chatai/upload-document',
            files=files,
            data=data,
            headers=headers
        )
        
        return response.json()

# Query documents
def query_documents(query, api_key):
    params = {
        'apikey': api_key,
        'query': query,
        'stream': 'false'
    }
    
    response = requests.get(
        'http://localhost:3001/api/v1/',
        params=params
    )
    
    return response.json()
```

### cURL Examples
```bash
# Upload document
curl -X POST http://localhost:3000/users/app/chatai/upload-document \
  -H "Authorization: Bearer <token>" \
  -F "file=@document.pdf" \
  -F "appId=app_123"

# Query with streaming
curl -N "http://localhost:3001/api/v1/?apikey=test_key&query=What%20is%20the%20total%20amount&stream=true"

# Query without streaming
curl "http://localhost:3001/api/v1/?apikey=test_key&query=What%20is%20the%20total%20amount&stream=false"
```
