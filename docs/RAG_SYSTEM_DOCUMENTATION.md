# RAG System Complete Documentation

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture Components](#architecture-components)
3. [Service Details](#service-details)
4. [API Documentation](#api-documentation)
5. [Data Flow Processes](#data-flow-processes)
6. [Deployment Guide](#deployment-guide)
7. [Configuration](#configuration)
8. [Troubleshooting](#troubleshooting)

## System Overview

The RAG (Retrieval-Augmented Generation) system is a distributed architecture designed for intelligent document processing and querying. It combines document parsing, vector embeddings, similarity search, and LLM-powered responses to provide contextual answers from uploaded documents.

### Key Features

- **Document Upload & Processing**: Supports PDF, DOCX, TXT files up to 20MB
- **Intelligent Chunking**: Page-based and semantic chunking with junk filtering
- **Vector Search**: High-performance similarity search using Qdrant
- **Streaming Responses**: Real-time response generation
- **Tenant Isolation**: Multi-tenant architecture with AppId-based separation
- **Batch Processing**: Optimized embedding generation with retry mechanisms

### Technology Stack

- **Backend**: Node.js (Express.js, NestJS)
- **Databases**: PostgreSQL, Qdrant Vector Database
- **Message Queue**: RabbitMQ
- **External APIs**: LlamaIndex, SambaNova, OpenRouter
- **Containerization**: Docker & Docker Compose

## Architecture Components

### Core Services

| Service              | Port | Technology       | Purpose                                                |
| -------------------- | ---- | ---------------- | ------------------------------------------------------ |
| **User-Service**     | 3000 | NestJS + TypeORM | User management, document metadata, authentication     |
| **ChatAI-SDK-Clean** | 3001 | Express.js       | Document processing, vector operations, query handling |

### Data Storage

| Component            | Port       | Purpose                                    |
| -------------------- | ---------- | ------------------------------------------ |
| **PostgreSQL**       | 5433       | User data, applications, document metadata |
| **Qdrant Vector DB** | 6333/6334  | Document embeddings, similarity search     |
| **RabbitMQ**         | 5672/15672 | Message queue for async processing         |

### External APIs

| API                  | Purpose                                | Rate Limits               |
| -------------------- | -------------------------------------- | ------------------------- |
| **LlamaIndex Cloud** | Document parsing and text extraction   | -                         |
| **SambaNova API**    | Embedding generation (1024 dimensions) | 30 RPM, 150 RPH, 1800 RPD |
| **OpenRouter API**   | LLM response generation                | -                         |

## Service Details

### User-Service (Port 3000)

**Technology**: NestJS with TypeORM
**Database**: PostgreSQL
**Purpose**: User management, document metadata, API key validation

#### Key Responsibilities:

- User authentication and authorization
- Document metadata storage
- API key validation
- Application management
- Internal API for service communication

#### Database Entities:

- `User`: User accounts and profiles
- `Application`: User applications with API keys
- `ChatAiDocument`: Document metadata and status
- `ChatAiMessage`: Chat history
- `ChatAiCreditUsage`: Usage tracking

### ChatAI-SDK-Clean (Port 3001)

**Technology**: Express.js
**Purpose**: Main processing engine for documents and queries

#### Key Responsibilities:

- Document processing pipeline
- Vector embedding generation
- Qdrant vector database operations
- Query processing and response generation
- Streaming response handling

#### Core Services:

- `llamaParseService`: Document parsing integration
- `embeddingService`: SambaNova API integration
- `qdrantService`: Vector database operations
- `vectorSearchService`: Similarity search logic
- `openRouterService`: LLM response generation

## API Documentation

### Document Upload API

#### Upload Document

```http
POST /users/app/chatai/upload-document
Content-Type: multipart/form-data
Authorization: Bearer <token>

Parameters:
- file: Document file (max 20MB)
- appId: Application ID
- filename: Optional filename override
```

**Response:**

```json
{
  "success": true,
  "message": "Document uploaded successfully",
  "data": {
    "documentId": "uuid",
    "filename": "document.pdf",
    "status": "parsing"
  }
}
```

#### Complete Processing (Internal)

```http
POST /upload-and-process
Content-Type: multipart/form-data

Parameters:
- file: Document file buffer
- appId: Application ID
- documentId: Document UUID
- userId: User ID
- filename: Document filename
```

### Query API

#### Main Query Endpoint

```http
GET /api/v1/?apikey=<key>&query=<query>&stream=true&sessionId=<id>

Parameters:
- apikey: Application API key (required)
- query: User question (required)
- stream: Enable streaming (default: true)
- sessionId: Session identifier (optional)
- testMode: Bypass user service (default: false)
```

**Streaming Response:**

```
data: {"type":"content","content":"Hello"}
data: {"type":"content","content":" world"}
data: {"type":"done","timestamp":"2024-01-01T00:00:00.000Z","timing":{"total":1500}}
```

**Non-streaming Response:**

```json
{
  "response": "Generated answer based on document context",
  "metadata": {
    "documentsUsed": 3,
    "contextLength": 2048,
    "timing": {
      "total": 1500,
      "vectorSearch": 200,
      "llmGeneration": 800
    }
  }
}
```

### Health Check API

#### System Health

```http
GET /health
```

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "ChatAI SDK Clean with Qdrant Vector Database",
  "endpoints": {
    "main": "/api/v1/?apikey=...&query=...",
    "health": "/health"
  },
  "vectorDatabase": {
    "status": "healthy",
    "stats": {
      "collections": 1,
      "points": 1250
    }
  }
}
```

## Data Flow Processes

### Document Upload Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant US as User-Service
    participant CS as ChatAI-SDK
    participant LI as LlamaIndex
    participant SN as SambaNova
    participant QD as Qdrant
    participant PG as PostgreSQL

    C->>US: 1. Upload Document (20MB max)
    US->>CS: 2. Forward for Processing
    CS->>LI: 3. Parse Document (streaming)
    LI->>CS: 4. Parsed Text + Pages
    CS->>CS: 5. Chunk & Filter Text
    CS->>SN: 6. Generate Embeddings (batch)
    SN->>CS: 7. Vector Embeddings (1024D)
    CS->>QD: 8. Store Vectors (batch upsert)
    CS->>US: 9. Status Updates (async)
    US->>PG: 10. Store Metadata
```

### Query Processing Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant CS as ChatAI-SDK
    participant US as User-Service
    participant SN as SambaNova
    participant QD as Qdrant
    participant OR as OpenRouter

    C->>CS: 1. Query Request (streaming)
    CS->>US: 2. Validate API Key
    US->>CS: 3. User + Documents
    CS->>SN: 4. Generate Query Embedding
    SN->>CS: 5. Query Vector (1024D)
    CS->>QD: 6. Vector Search (similarity)
    QD->>CS: 7. Relevant Chunks (scored)
    CS->>CS: 8. Format Context
    CS->>OR: 9. Generate Response (streaming)
    OR->>CS: 10. Streaming Response
    CS->>C: 11. Stream to Client
```

## Deployment Guide

### Prerequisites

- Docker & Docker Compose
- Node.js 18+ (for local development)
- Git

### Environment Setup

#### 1. Clone Repository

```bash
git clone <repository-url>
cd RAG/under_construction
```

#### 2. Start Infrastructure Services

```bash
# Start databases and message queue
docker-compose up -d postgres qdrant rabbitmq
```

#### 3. Configure Environment Variables

**User-Service (.env)**

```env
# Server
PORT=3000
NODE_ENV=development

# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=abstraxn

# JWT & Security
JWT_SECRET=your_jwt_secret
PASSWORD=your_encryption_password

# RabbitMQ
RMQ_URL=amqp://admin:admin123@localhost:5672

# ChatAI Integration
CHATAI_ORIGIN=http://localhost:3001
INTERNAL_API_KEY=chatai-internal-2024

# Qdrant
QDRANT_URL=http://localhost:6333
```

**ChatAI-SDK-Clean (.env)**

```env
# Server
PORT=3001
NODE_ENV=development

# Services
USER_SERVICE_URL=http://localhost:3000
CHATAI_ORIGIN=http://localhost:3001

# External APIs
LLAMA_CLOUD_API_KEY=llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp
OPENROUTER_API_KEY=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
SAMBANOVA_API_KEY=your_sambanova_api_key

# Vector Database
QDRANT_URL=http://localhost:6333
QDRANT_COLLECTION=chatai_documents

# Cache & Rate Limiting
CACHE_TTL_MINUTES=15
MAX_SESSIONS=1000
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100
```

#### 4. Start Services

**User-Service:**

```bash
cd User-Service
npm install
npm run start:dev
```

**ChatAI-SDK-Clean:**

```bash
cd ChatAI-SDK-Clean
npm install
npm run dev
```

### Docker Deployment

#### Complete Stack

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Individual Services

```bash
# Database only
docker-compose up -d postgres qdrant

# Message queue
docker-compose up -d rabbitmq

# Check service health
curl http://localhost:3001/health
```

## Configuration

### Service Ports

| Service             | Port  | Protocol |
| ------------------- | ----- | -------- |
| User-Service        | 3000  | HTTP     |
| ChatAI-SDK-Clean    | 3001  | HTTP     |
| PostgreSQL          | 5433  | TCP      |
| Qdrant HTTP         | 6333  | HTTP     |
| Qdrant gRPC         | 6334  | gRPC     |
| RabbitMQ            | 5672  | AMQP     |
| RabbitMQ Management | 15672 | HTTP     |

### External API Configuration

#### SambaNova API

- **Purpose**: Embedding generation
- **Model**: `text-embedding-ada-002` equivalent
- **Dimensions**: 1024
- **Rate Limits**: 30 RPM, 150 RPH, 1800 RPD
- **Batch Size**: 3 chunks per request

#### LlamaIndex Cloud

- **Purpose**: Document parsing
- **Supported Formats**: PDF, DOCX, TXT, PPTX
- **Max File Size**: 20MB
- **Response**: Streaming JSON

#### OpenRouter API

- **Purpose**: LLM response generation
- **Model**: `deepseek/deepseek-chat-v3-0324:free`
- **Features**: Streaming responses, context-aware

### Vector Database Configuration

#### Qdrant Settings

```javascript
{
  url: 'http://localhost:6333',
  collectionName: 'chatai_documents',
  vectorSize: 1024,
  distance: 'Cosine',
  indexing: {
    threshold: 20000,
    memmap: true
  }
}
```

#### Collection Schema

```json
{
  "vectors": {
    "size": 1024,
    "distance": "Cosine"
  },
  "payload": {
    "text": "keyword",
    "appId": "keyword",
    "documentId": "keyword",
    "filename": "keyword",
    "userId": "keyword",
    "chunkIndex": "integer",
    "page": "integer",
    "timestamp": "datetime"
  }
}
```

## Troubleshooting

### Common Issues

#### 1. Service Connection Errors

**Problem**: Services cannot connect to databases
**Solution**:

```bash
# Check service status
docker-compose ps

# Restart specific service
docker-compose restart postgres

# Check logs
docker-compose logs postgres
```

#### 2. Vector Search Not Working

**Problem**: Qdrant returns no results
**Diagnostics**:

```bash
# Check Qdrant health
curl http://localhost:6333/health

# Check collection status
curl http://localhost:6333/collections/chatai_documents

# Verify embeddings
curl -X POST http://localhost:6333/collections/chatai_documents/points/search \
  -H "Content-Type: application/json" \
  -d '{"vector": [0.1, 0.2, ...], "limit": 5}'
```

#### 3. Document Processing Failures

**Problem**: Documents stuck in "parsing" status
**Diagnostics**:

```bash
# Check ChatAI-SDK logs
cd ChatAI-SDK-Clean && npm run dev

# Verify LlamaIndex API
curl -X POST https://api.cloud.llamaindex.ai/api/v1/parsing/upload \
  -H "Authorization: Bearer YOUR_API_KEY"

# Check SambaNova API
curl -X POST https://api.sambanova.ai/v1/embeddings \
  -H "Authorization: Bearer YOUR_API_KEY"
```

#### 4. API Key Validation Issues

**Problem**: "Invalid API key" errors
**Solution**:

```sql
-- Check API key in database
SELECT * FROM applications WHERE api_key = 'your_api_key';

-- Verify user service connection
curl -X POST http://localhost:3000/users/app/key-validator \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "your_api_key", "origin": "http://localhost:3001"}'
```

### Performance Optimization

#### 1. Embedding Generation

- Use batch processing (3 chunks per request)
- Implement retry mechanisms for failed batches
- Cache embeddings to avoid regeneration

#### 2. Vector Search

- Adjust similarity thresholds based on use case
- Implement result caching for common queries
- Use appropriate search limits (15 recommended)

#### 3. Response Generation

- Enable streaming for better UX
- Implement session caching
- Use appropriate context window sizes

### Monitoring & Logging

#### Health Checks

```bash
# User Service health
curl http://localhost:3000/health

# ChatAI SDK health
curl http://localhost:3001/health

# Qdrant health
curl http://localhost:6333/health
```

#### Log Locations

- **User-Service**: Console output with NestJS logger
- **ChatAI-SDK-Clean**: Console output with custom logging
- **Docker Services**: `docker-compose logs <service>`

#### Performance Metrics

- Document processing time
- Vector search latency
- LLM response generation time
- API rate limit usage

### Backup & Recovery

#### Database Backup

```bash
# PostgreSQL backup
docker exec postgres pg_dump -U postgres abstraxn > backup.sql

# Qdrant backup
curl -X POST http://localhost:6333/collections/chatai_documents/snapshots
```

#### Data Recovery

```bash
# PostgreSQL restore
docker exec -i postgres psql -U postgres abstraxn < backup.sql

# Qdrant restore
curl -X PUT http://localhost:6333/collections/chatai_documents/snapshots/upload \
  -H "Content-Type: application/octet-stream" \
  --data-binary @snapshot.tar
```

---

## Support & Maintenance

For technical support or questions about this documentation, please refer to:

- System logs for error diagnostics
- API health endpoints for service status
- Performance monitoring dashboards
- Database query tools for data verification

**Last Updated**: 2024-01-01
**Version**: 1.0.0
