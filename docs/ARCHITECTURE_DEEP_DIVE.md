# RAG System Architecture Deep Dive

## Overview

This document provides an in-depth technical analysis of the RAG system architecture, including design decisions, data flows, performance considerations, and scalability patterns.

## System Architecture Principles

### 1. Microservices Architecture
The system follows a microservices pattern with clear separation of concerns:

- **User-Service**: Authentication, user management, document metadata
- **ChatAI-SDK-Clean**: Document processing, vector operations, query handling
- **External Services**: Specialized APIs for parsing, embeddings, and LLM responses

### 2. Event-Driven Communication
Services communicate through:
- **Synchronous APIs**: For real-time operations (queries, validation)
- **Asynchronous Updates**: For status notifications and background processing
- **Message Queues**: For reliable task distribution

### 3. Data Separation
- **Relational Data**: PostgreSQL for structured user/app data
- **Vector Data**: Qdrant for high-dimensional embeddings
- **Cache Data**: In-memory caching for performance optimization

## Component Deep Dive

### User-Service Architecture

#### Technology Stack
- **Framework**: NestJS (Node.js)
- **ORM**: TypeORM
- **Database**: PostgreSQL
- **Authentication**: JWT tokens
- **Validation**: Class-validator decorators

#### Core Modules
```typescript
// Module structure
AppModule
├── UserModule
├── ApplicationModule
├── ChatAiModule
├── VectorModule
└── MailModule
```

#### Database Schema
```sql
-- Core entities
Users (id, email, password_hash, created_at, updated_at)
Applications (id, user_id, name, api_key, created_at)
ChatAiDocuments (id, app_id, user_id, filename, status, parsed_data, created_at)
ChatAiMessages (id, app_id, user_id, query, response, created_at)
ChatAiCreditUsage (id, app_id, operation_type, credits_used, created_at)
```

#### API Design Patterns
- **RESTful endpoints** with consistent naming
- **DTO validation** for request/response objects
- **Middleware chains** for authentication and logging
- **Exception filters** for standardized error handling

### ChatAI-SDK-Clean Architecture

#### Technology Stack
- **Framework**: Express.js
- **Processing**: Stream-based document handling
- **Vector Operations**: Qdrant client integration
- **Caching**: In-memory LRU cache
- **Rate Limiting**: Token bucket algorithm

#### Service Layer Architecture
```javascript
// Service organization
services/
├── llamaParseService.js      // Document parsing
├── embeddingService.js       // Vector generation
├── qdrantService.js          // Vector database ops
├── vectorSearchService.js    // Search logic
├── openRouterService.js      // LLM integration
└── cacheService.js           // Performance caching
```

#### Processing Pipeline
```mermaid
graph LR
    A[Document Upload] --> B[LlamaIndex Parsing]
    B --> C[Text Chunking]
    C --> D[Junk Filtering]
    D --> E[Batch Embedding]
    E --> F[Vector Storage]
    F --> G[Status Update]
```

## Data Flow Analysis

### Document Processing Flow

#### Phase 1: Upload & Validation
1. **Client Upload** → User-Service
   - File validation (size, type, permissions)
   - User authentication and authorization
   - Document metadata creation

2. **Processing Handoff** → ChatAI-SDK-Clean
   - File buffer transfer (no disk storage)
   - Metadata enrichment
   - Processing job initialization

#### Phase 2: Parsing & Extraction
3. **LlamaIndex Integration**
   - Streaming API calls for large documents
   - Multi-format support (PDF, DOCX, TXT, PPTX)
   - Structured data extraction (text, pages, metadata)

4. **Content Processing**
   - Page-based chunking (preferred strategy)
   - Semantic chunking (fallback for complex documents)
   - Junk filtering (short pages, formatting artifacts)
   - Page merging for small adjacent content

#### Phase 3: Vectorization
5. **Embedding Generation**
   - SambaNova API integration
   - Batch processing (3 chunks per request)
   - Retry mechanisms with exponential backoff
   - Rate limit compliance (30 RPM, 150 RPH, 1800 RPD)

6. **Vector Storage**
   - Qdrant batch upsert operations
   - Tenant isolation via appId filtering
   - Metadata preservation for retrieval context

### Query Processing Flow

#### Phase 1: Authentication & Setup
1. **Request Validation**
   - API key verification via User-Service
   - Rate limiting enforcement
   - Session management

2. **Context Preparation**
   - User document retrieval
   - Permission validation
   - Cache lookup for optimization

#### Phase 2: Vector Search
3. **Query Vectorization**
   - Single embedding generation for user query
   - Same vector space as document embeddings (1024D)

4. **Similarity Search**
   - Cosine similarity in Qdrant
   - Configurable thresholds and limits
   - Tenant-isolated search (appId filtering)

#### Phase 3: Response Generation
5. **Context Formation**
   - Intelligent chunk ranking
   - Quality filtering for relevance
   - Context window optimization

6. **LLM Integration**
   - OpenRouter API streaming
   - Context-aware prompt engineering
   - Real-time response streaming to client

## Performance Optimizations

### Caching Strategy

#### Multi-Level Caching
```javascript
// Cache hierarchy
L1: In-memory LRU cache (API keys, embeddings)
L2: Session cache (conversation history)
L3: Vector search cache (common queries)
```

#### Cache Implementation
```javascript
class CacheService {
  constructor() {
    this.apiKeyCache = new LRU({ max: 1000, ttl: 15 * 60 * 1000 });
    this.embeddingCache = new LRU({ max: 5000, ttl: 60 * 60 * 1000 });
    this.sessionCache = new LRU({ max: 1000, ttl: 30 * 60 * 1000 });
  }
}
```

### Batch Processing

#### Embedding Generation
- **Parallel processing**: Multiple batches simultaneously
- **Retry logic**: Failed batch recovery without losing progress
- **Progress tracking**: Real-time status updates

#### Vector Storage
- **Bulk operations**: Qdrant batch upsert for efficiency
- **Connection pooling**: Optimized database connections
- **Transaction management**: Atomic operations for consistency

### Streaming Optimizations

#### Response Streaming
```javascript
// Server-Sent Events implementation
async function* streamResponse(query, context) {
  const stream = await openRouterService.generateStreamingResponse(query, context);
  
  for await (const chunk of stream) {
    yield {
      type: 'content',
      content: chunk,
      timestamp: new Date().toISOString()
    };
  }
  
  yield {
    type: 'done',
    timing: { total: Date.now() - startTime }
  };
}
```

## Scalability Considerations

### Horizontal Scaling

#### Service Scaling
- **Stateless design**: Services can be replicated without shared state
- **Load balancing**: Round-robin or least-connections algorithms
- **Auto-scaling**: Based on CPU/memory metrics

#### Database Scaling
- **Read replicas**: For query-heavy workloads
- **Sharding**: Vector data partitioning by appId
- **Connection pooling**: Efficient resource utilization

### Vertical Scaling

#### Resource Optimization
- **Memory management**: Efficient buffer handling for large documents
- **CPU utilization**: Parallel processing for embedding generation
- **I/O optimization**: Streaming for large file processing

### External Service Management

#### Rate Limit Handling
```javascript
class RateLimitManager {
  constructor() {
    this.limits = {
      sambanova: { rpm: 30, rph: 150, rpd: 1800 },
      openrouter: { rpm: 60, rph: 3600 }
    };
    this.counters = new Map();
  }
  
  async checkLimit(service) {
    // Implementation with sliding window
  }
}
```

#### Fallback Strategies
- **Service degradation**: Graceful handling of API failures
- **Alternative providers**: Backup embedding/LLM services
- **Circuit breakers**: Prevent cascade failures

## Security Architecture

### Authentication & Authorization

#### Multi-Layer Security
1. **API Key Validation**: Application-level authentication
2. **JWT Tokens**: User session management
3. **Internal API Keys**: Service-to-service communication

#### Tenant Isolation
```javascript
// Vector search with tenant filtering
const searchResults = await qdrantClient.search(collectionName, {
  vector: queryEmbedding,
  filter: {
    must: [{ key: 'appId', match: { value: userAppId } }]
  },
  limit: 15,
  score_threshold: 0.3
});
```

### Data Protection

#### Encryption
- **At Rest**: Database encryption for sensitive data
- **In Transit**: HTTPS/TLS for all communications
- **API Keys**: Hashed storage with secure comparison

#### Privacy Compliance
- **Data Minimization**: Only necessary data collection
- **Retention Policies**: Configurable document lifecycle
- **Audit Logging**: Comprehensive access tracking

## Monitoring & Observability

### Metrics Collection

#### Application Metrics
```javascript
// Performance tracking
const metrics = {
  documentProcessing: {
    totalTime: Date.now() - startTime,
    parsingTime: parseEndTime - parseStartTime,
    embeddingTime: embeddingEndTime - embeddingStartTime,
    storageTime: storageEndTime - storageStartTime
  },
  queryProcessing: {
    vectorSearchTime: searchEndTime - searchStartTime,
    llmGenerationTime: llmEndTime - llmStartTime,
    totalResponseTime: responseEndTime - requestStartTime
  }
};
```

#### System Metrics
- **Resource utilization**: CPU, memory, disk, network
- **Service health**: Uptime, response times, error rates
- **External dependencies**: API response times, rate limit usage

### Error Handling

#### Structured Error Responses
```javascript
class APIError extends Error {
  constructor(message, code, statusCode, details = {}) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}
```

#### Error Recovery
- **Retry mechanisms**: Exponential backoff for transient failures
- **Circuit breakers**: Prevent cascade failures
- **Graceful degradation**: Fallback responses when services unavailable

## Future Architecture Considerations

### Scalability Enhancements
- **Microservice decomposition**: Further service separation
- **Event sourcing**: Complete audit trail and replay capability
- **CQRS pattern**: Separate read/write optimization

### Performance Improvements
- **Edge caching**: CDN for static responses
- **Precomputed embeddings**: Common query optimization
- **Async processing**: Background job queues for heavy operations

### Technology Evolution
- **Vector database alternatives**: Evaluation of Pinecone, Weaviate
- **LLM integration**: Direct model hosting vs. API services
- **Real-time updates**: WebSocket connections for live status

This architecture deep dive provides the technical foundation for understanding, maintaining, and evolving the RAG system as requirements and scale change over time.
