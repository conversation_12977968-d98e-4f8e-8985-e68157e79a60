# RAG System Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the RAG system in different environments, from local development to production.

## Prerequisites

### System Requirements
- **OS**: Linux, macOS, or Windows with WSL2
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: 20GB free space
- **CPU**: 4+ cores recommended

### Software Dependencies
- **Docker**: 20.10+ with Docker Compose
- **Node.js**: 18+ (for local development)
- **Git**: Latest version
- **curl**: For API testing

## Local Development Setup

### 1. Repository Setup
```bash
# Clone the repository
git clone <repository-url>
cd RAG/under_construction

# Verify directory structure
ls -la
# Should show: User-Service/, ChatAI-SDK-Clean/, docker-compose.yml
```

### 2. Infrastructure Services
Start the required databases and message queue:

```bash
# Start infrastructure services
docker-compose up -d postgres qdrant rabbitmq

# Verify services are running
docker-compose ps

# Check service logs
docker-compose logs postgres
docker-compose logs qdrant
docker-compose logs rabbitmq
```

### 3. Environment Configuration

#### User-Service Environment
Create `User-Service/.env`:
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=abstraxn

# Security
JWT_SECRET=your_super_secret_jwt_key_here
PASSWORD=your_encryption_password_here

# Message Queue
RMQ_URL=amqp://admin:admin123@localhost:5672

# ChatAI Integration
CHATAI_ORIGIN=http://localhost:3001
INTERNAL_API_KEY=chatai-internal-2024

# Vector Database
QDRANT_URL=http://localhost:6333

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>
PROJECT_NAME=RAG System
FRONTEND_URL=http://localhost:3000
```

#### ChatAI-SDK-Clean Environment
Create `ChatAI-SDK-Clean/.env`:
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Service URLs
USER_SERVICE_URL=http://localhost:3000
CHATAI_ORIGIN=http://localhost:3001

# External API Keys
LLAMA_CLOUD_API_KEY=llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp
OPENROUTER_API_KEY=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
SAMBANOVA_API_KEY=your_sambanova_api_key_here

# Vector Database
QDRANT_URL=http://localhost:6333
QDRANT_COLLECTION=chatai_documents

# Performance Configuration
CACHE_TTL_MINUTES=15
MAX_SESSIONS=1000
CLEANUP_INTERVAL_MINUTES=5

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
```

### 4. Service Installation & Startup

#### User-Service
```bash
cd User-Service

# Install dependencies
npm install

# Run database migrations (if any)
npm run migration:run

# Start in development mode
npm run start:dev

# Verify service is running
curl http://localhost:3000/health
```

#### ChatAI-SDK-Clean
```bash
cd ChatAI-SDK-Clean

# Install dependencies
npm install

# Start in development mode
npm run dev

# Verify service is running
curl http://localhost:3001/health
```

### 5. Verification & Testing

#### Health Checks
```bash
# Check all services
curl http://localhost:3000/health  # User-Service
curl http://localhost:3001/health  # ChatAI-SDK-Clean
curl http://localhost:6333/health  # Qdrant
curl http://localhost:15672        # RabbitMQ Management UI
```

#### Database Verification
```bash
# PostgreSQL connection test
docker exec -it postgres psql -U postgres -d abstraxn -c "\dt"

# Qdrant collection check
curl http://localhost:6333/collections
```

#### End-to-End Test
```bash
# Test vector search integration
curl "http://localhost:3001/api/v1/?testMode=true&query=test%20query"
```

## Docker Deployment

### 1. Complete Stack Deployment
```bash
# Build and start all services
docker-compose up -d --build

# View logs for all services
docker-compose logs -f

# Check service status
docker-compose ps
```

### 2. Individual Service Management
```bash
# Start specific services
docker-compose up -d postgres qdrant
docker-compose up -d rabbitmq

# Restart a service
docker-compose restart user-service

# View logs for specific service
docker-compose logs -f chatai-sdk

# Scale services (if configured)
docker-compose up -d --scale chatai-sdk=2
```

### 3. Docker Environment Variables
Create `.env` file in project root:
```env
# Service Versions
USER_SERVICE_IMAGE=user-service:latest
CHATAI_SDK_IMAGE=chatai-sdk:latest

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=abstraxn

# RabbitMQ Configuration
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin123

# External API Keys
LLAMA_CLOUD_API_KEY=your_key_here
OPENROUTER_API_KEY=your_key_here
SAMBANOVA_API_KEY=your_key_here
```

## Production Deployment

### 1. Environment Preparation

#### Server Requirements
- **CPU**: 8+ cores
- **Memory**: 32GB+ RAM
- **Storage**: 100GB+ SSD
- **Network**: High-bandwidth connection

#### Security Configuration
```bash
# Create non-root user
sudo useradd -m -s /bin/bash raguser
sudo usermod -aG docker raguser

# Set up firewall
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# Configure SSL certificates (using Let's Encrypt)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### 2. Production Environment Variables

#### Secure Configuration
```env
# Production Security
NODE_ENV=production
JWT_SECRET=your_very_long_and_secure_jwt_secret_here
PASSWORD=your_very_secure_encryption_password_here

# Database (Use managed service in production)
POSTGRES_HOST=your-postgres-host.com
POSTGRES_PORT=5432
POSTGRES_USER=raguser
POSTGRES_PASSWORD=your_secure_db_password
POSTGRES_DB=rag_production

# Redis (for production caching)
REDIS_URL=redis://your-redis-host:6379

# External Services
QDRANT_URL=https://your-qdrant-cluster.com
RABBITMQ_URL=amqp://user:pass@your-rabbitmq-host:5672

# API Keys (use environment-specific keys)
LLAMA_CLOUD_API_KEY=prod_llama_key
OPENROUTER_API_KEY=prod_openrouter_key
SAMBANOVA_API_KEY=prod_sambanova_key

# Monitoring
LOG_LEVEL=warn
SENTRY_DSN=your_sentry_dsn_here
```

### 3. Production Docker Compose

Create `docker-compose.prod.yml`:
```yaml
version: "3.8"

services:
  user-service:
    build:
      context: ./User-Service
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    restart: unless-stopped
    depends_on:
      - postgres
      - rabbitmq
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  chatai-sdk:
    build:
      context: ./ChatAI-SDK-Clean
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    restart: unless-stopped
    depends_on:
      - user-service
      - qdrant
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - user-service
      - chatai-sdk
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped

volumes:
  postgres_data:
  qdrant_data:
```

### 4. Nginx Configuration

Create `nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream user_service {
        server user-service:3000;
    }
    
    upstream chatai_service {
        server chatai-sdk:3001;
    }
    
    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl;
        server_name your-domain.com;
        
        ssl_certificate /etc/ssl/certs/fullchain.pem;
        ssl_certificate_key /etc/ssl/certs/privkey.pem;
        
        location /api/v1/ {
            proxy_pass http://chatai_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Enable streaming
            proxy_buffering off;
            proxy_cache off;
        }
        
        location /users/ {
            proxy_pass http://user_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /health {
            proxy_pass http://chatai_service;
        }
    }
}
```

### 5. Production Deployment Commands
```bash
# Deploy to production
docker-compose -f docker-compose.prod.yml up -d --build

# Monitor deployment
docker-compose -f docker-compose.prod.yml logs -f

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Backup data
docker exec postgres pg_dump -U postgres rag_production > backup.sql
curl -X POST http://localhost:6333/collections/chatai_documents/snapshots
```

## Monitoring & Maintenance

### 1. Health Monitoring
```bash
# Automated health check script
#!/bin/bash
services=("http://localhost:3000/health" "http://localhost:3001/health" "http://localhost:6333/health")

for service in "${services[@]}"; do
    if curl -f "$service" > /dev/null 2>&1; then
        echo "✅ $service is healthy"
    else
        echo "❌ $service is down"
        # Send alert (email, Slack, etc.)
    fi
done
```

### 2. Log Management
```bash
# Centralized logging with Docker
docker-compose logs --tail=100 -f

# Log rotation configuration
echo '{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}' | sudo tee /etc/docker/daemon.json
```

### 3. Backup Strategy
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)

# PostgreSQL backup
docker exec postgres pg_dump -U postgres rag_production > "backup_postgres_$DATE.sql"

# Qdrant backup
curl -X POST http://localhost:6333/collections/chatai_documents/snapshots

# Compress and store
tar -czf "rag_backup_$DATE.tar.gz" backup_postgres_$DATE.sql qdrant_snapshots/

# Upload to cloud storage (AWS S3, Google Cloud, etc.)
aws s3 cp "rag_backup_$DATE.tar.gz" s3://your-backup-bucket/
```

### 4. Performance Monitoring
```bash
# Resource usage monitoring
docker stats

# Database performance
docker exec postgres psql -U postgres -d rag_production -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"

# Vector database stats
curl http://localhost:6333/collections/chatai_documents
```

## Troubleshooting

### Common Deployment Issues

#### 1. Port Conflicts
```bash
# Check port usage
sudo netstat -tulpn | grep :3000
sudo netstat -tulpn | grep :3001

# Kill conflicting processes
sudo kill -9 $(sudo lsof -t -i:3000)
```

#### 2. Docker Issues
```bash
# Clean Docker system
docker system prune -a

# Rebuild without cache
docker-compose build --no-cache

# Check Docker logs
docker-compose logs service-name
```

#### 3. Database Connection Issues
```bash
# Test PostgreSQL connection
docker exec -it postgres psql -U postgres -d abstraxn

# Check Qdrant status
curl http://localhost:6333/health

# Verify network connectivity
docker network ls
docker network inspect rag_default
```

#### 4. Service Communication Issues
```bash
# Test internal service communication
docker exec chatai-sdk curl http://user-service:3000/health

# Check environment variables
docker exec user-service env | grep POSTGRES
```

This deployment guide provides comprehensive instructions for setting up the RAG system in various environments, from local development to production deployment with proper security, monitoring, and maintenance procedures.
