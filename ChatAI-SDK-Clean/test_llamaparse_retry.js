/**
 * Test script to verify LlamaParse retry logic
 */

const llamaParseService = require('./src/services/llamaParseService');

// Mock test cases for different error scenarios
const testCases = [
  {
    name: 'DNS Resolution Error (EAI_AGAIN)',
    error: new Error('request to https://api.cloud.llamaindex.ai/api/v1/parsing/upload failed, reason: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai'),
    expectedRetryable: true
  },
  {
    name: 'Network Timeout',
    error: new Error('request timeout of 30000ms exceeded'),
    expectedRetryable: true
  },
  {
    name: 'Connection Reset',
    error: new Error('socket hang up'),
    expectedRetryable: true
  },
  {
    name: 'Connection Refused',
    error: new Error('connect ECONNREFUSED 127.0.0.1:443'),
    expectedRetryable: true
  },
  {
    name: 'Server Error (5xx)',
    error: new Error('Upload failed: 500 - Internal Server Error'),
    expectedRetryable: true
  },
  {
    name: 'Rate Limit Error',
    error: new Error('Upload failed: 429 - Too Many Requests'),
    expectedRetryable: false // Rate limits should be handled differently
  },
  {
    name: 'Authentication Error',
    error: new Error('Upload failed: 401 - Unauthorized'),
    expectedRetryable: false
  },
  {
    name: 'Invalid File Format',
    error: new Error('Upload failed: 400 - Unsupported file format'),
    expectedRetryable: false
  }
];

console.log('🧪 Testing LlamaParse Retry Logic');
console.log('=================================\n');

// Test error classification
console.log('📋 Testing Error Classification:');
console.log('--------------------------------');

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const isRetryable = llamaParseService.isRetryableError(testCase.error);
  const success = isRetryable === testCase.expectedRetryable;
  
  if (success) {
    console.log(`✅ Test ${index + 1}: ${testCase.name}`);
    console.log(`   Error: "${testCase.error.message.substring(0, 60)}..."`);
    console.log(`   Expected: ${testCase.expectedRetryable ? 'Retryable' : 'Non-retryable'}, Got: ${isRetryable ? 'Retryable' : 'Non-retryable'}\n`);
    passed++;
  } else {
    console.log(`❌ Test ${index + 1}: ${testCase.name}`);
    console.log(`   Error: "${testCase.error.message.substring(0, 60)}..."`);
    console.log(`   Expected: ${testCase.expectedRetryable ? 'Retryable' : 'Non-retryable'}, Got: ${isRetryable ? 'Retryable' : 'Non-retryable'}\n`);
    failed++;
  }
});

console.log('📊 Error Classification Results:');
console.log(`✅ Passed: ${passed}/${testCases.length}`);
console.log(`❌ Failed: ${failed}/${testCases.length}`);
console.log(`🎯 Success Rate: ${Math.round((passed / testCases.length) * 100)}%\n`);

// Test retry delay calculation
console.log('⏱️ Testing Retry Delay Calculation:');
console.log('-----------------------------------');

const delayTests = [
  { attempt: 0, errorType: 'network', expectedMin: 2000, expectedMax: 4000 },
  { attempt: 1, errorType: 'network', expectedMin: 4000, expectedMax: 6000 },
  { attempt: 2, errorType: 'network', expectedMin: 8000, expectedMax: 10000 },
  { attempt: 0, errorType: 'eai_again', expectedMin: 5000, expectedMax: 7000 },
  { attempt: 0, errorType: 'rate limit', expectedMin: 60000, expectedMax: 65000 }
];

delayTests.forEach((test, index) => {
  const mockError = new Error(`Test ${test.errorType} error`);
  const delay = llamaParseService.calculateRetryDelay(test.attempt, mockError);
  const inRange = delay >= test.expectedMin && delay <= test.expectedMax;
  
  console.log(`${inRange ? '✅' : '❌'} Delay Test ${index + 1}: Attempt ${test.attempt}, ${test.errorType}`);
  console.log(`   Calculated delay: ${delay}ms (expected: ${test.expectedMin}-${test.expectedMax}ms)`);
});

console.log('\n🔧 Retry Configuration Summary:');
console.log('==============================');
console.log('📊 Max retries per parsing attempt: 3');
console.log('📊 Max retries per status check: 2');
console.log('📊 Max retries per result fetch: 2');
console.log('⏱️ Base retry delay: 2 seconds (with exponential backoff)');
console.log('⏱️ Max retry delay: 30 seconds');
console.log('⏱️ DNS error minimum delay: 5 seconds');
console.log('⏱️ Rate limit minimum delay: 60 seconds');

console.log('\n🎯 Retryable Error Patterns:');
console.log('============================');
const retryablePatterns = [
  'eai_again (DNS resolution failure)',
  'enotfound (DNS lookup failed)',
  'econnreset (Connection reset)',
  'econnrefused (Connection refused)',
  'etimedout (Request timeout)',
  'timeout (General timeout)',
  'network (Network error)',
  'fetch failed (Fetch failure)',
  'socket hang up (Socket error)',
  'getaddrinfo (DNS resolution error)',
  'status check failed (Status check network error)',
  'result fetch failed (Result fetch network error)',
  'upload failed: 5xx (Server errors)',
  'parsing failed: network (Network-related parsing failures)'
];

retryablePatterns.forEach(pattern => {
  console.log(`   • ${pattern}`);
});

console.log('\n💡 Usage Example:');
console.log('=================');
console.log('When you encounter the error:');
console.log('❌ LlamaParse error for LLM24aug.pdf: request to https://api.cloud.llamaindex.ai/api/v1/parsing/upload failed, reason: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai');
console.log('');
console.log('The enhanced service will now:');
console.log('🔄 Attempt 1 failed: getaddrinfo EAI_AGAIN...');
console.log('🔄 Retrying in 5.2 seconds... (2 attempts remaining)');
console.log('🔄 Attempt 2 failed: getaddrinfo EAI_AGAIN...');
console.log('🔄 Retrying in 10.8 seconds... (1 attempts remaining)');
console.log('✅ Attempt 3 succeeded: File uploaded successfully');

if (failed === 0) {
  console.log('\n🎉 All tests passed! LlamaParse retry logic is working correctly.');
} else {
  console.log('\n⚠️ Some tests failed. The retry logic may need adjustment.');
}
