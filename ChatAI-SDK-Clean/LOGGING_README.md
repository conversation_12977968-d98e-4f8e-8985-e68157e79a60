# Document Filtering Logs

## Quick Start

The system now automatically logs all data that gets filtered during document processing. This helps you understand what content is being removed and why.

### 🚀 Immediate Usage

1. **Upload a document** through the normal API
2. **Check the logs** to see what was filtered:
   ```bash
   node analyze-logs.js
   ```

### 📁 Log Locations

All logs are stored in the `logs/` directory:

- **Page filtering**: `logs/document-processing/page-filtering/`
- **Chunk filtering**: `logs/document-processing/chunk-filtering/`
- **Processing summaries**: `logs/document-processing/processing-summary/`
- **Errors/warnings**: `logs/general/`

### 🔍 What Gets Logged

#### Page Filtering

- Pages with too few words (< 20 words)
- Header-only pages (e.g., "Chapter 5")
- Footer-only pages (e.g., "Page 10 of 50")
- Pages with low meaningful content
- Page merging operations

#### Chunk Filtering

- Table formatting artifacts (dashes, borders)
- Navigation elements (page numbers, TOC)
- Copyright notices
- Very short chunks (< 100 characters)
- High repetition content
- Empty/whitespace-only content

#### Processing Summary

- Total processing time
- Pages and chunks filtered
- Cost savings from filtering
- Overall success metrics

### 📊 Analysis Tools

#### Complete Analysis

```bash
node analyze-logs.js
```

#### Specific Analysis

```bash
node analyze-logs.js pages 7        # Page filtering for 7 days
node analyze-logs.js chunks 3       # Chunk filtering for 3 days
node analyze-logs.js examples chunk 10  # Recent chunk examples
```

#### Test the System

```bash
node test-logging.js
```

### 🔧 Configuration

Set these environment variables in your `.env` file:

```bash
# Enable detailed filtering logs (default: true)
ENABLE_DETAILED_FILTERING_LOGS=true

# Log level: DEBUG, INFO, WARN, ERROR (default: INFO)
LOG_LEVEL=INFO

# Max log file size before rotation (default: 10MB)
MAX_LOG_FILE_SIZE=10485760
```

### 📋 Example Log Entry

```json
{
  "timestamp": "2025-07-15T10:30:00.000Z",
  "type": "CHUNK_FILTERING",
  "level": "INFO",
  "document": {
    "filename": "research-paper.pdf",
    "uploadId": "doc-123"
  },
  "chunk": {
    "index": 15,
    "pageNumber": 5,
    "content": "----------Table----------",
    "wordCount": 2,
    "charCount": 24,
    "meaningfulRatio": 0.1,
    "filterReason": "Table formatting artifacts (100% artifact lines)"
  }
}
```

### 💡 Benefits

1. **Transparency**: See exactly what content gets filtered and why
2. **Optimization**: Adjust filtering criteria based on real data
3. **Debugging**: Understand why certain content isn't searchable
4. **Cost Tracking**: Monitor embedding quota savings from filtering

### 🔄 Real-time Console Output

During document processing, you'll see detailed real-time output showing exactly what gets filtered:

#### Page Filtering Output

```
📄 Filtered page 3: Too short (3 < 20 words)
   📊 3 words, 25 chars
   📝 "Chapter 1  Introduction  "

🔗 Merged page 12 with page 13 (forward)
   📊 4 → 25 words after merge
```

#### Chunk Filtering Output

```
🗑️  Filtered chunk 18: Copyright or legal notice
   📊 16 words, 95 chars
   📝 "© 2024 Research Institute. All rights re... [TRUNCATED]"

🗑️  Filtered chunk 25: Too short (4 < 100 chars)
   📊 1 words, 4 chars
   📝 "Fig."
```

#### Processing Summary

```
📊 Page Processing Summary:
   📄 Original pages: 15
   ✅ Pages kept: 11
   🗑️  Pages filtered: 3 (20%)
   🔗 Pages merged: 1
   💰 Processing efficiency: 73% content retained

📊 Content Filtering Summary:
   📝 Characters filtered: 350
   📝 Words filtered: 58
   💾 Storage saved: ~0 KB of text
   🔍 Quality improvement: Removed low-value content for better search results
```

### 🎬 See It In Action

Run the demo to see the complete filtering pipeline:

```bash
node demo-filtering.js
```

### 🔄 Real-time Log Monitoring

Watch logs in real-time during document processing:

```bash
# Watch chunk filtering
tail -f logs/document-processing/chunk-filtering/$(date +%Y-%m-%d)_chunk-filtering.log

# Watch page filtering
tail -f logs/document-processing/page-filtering/$(date +%Y-%m-%d)_page-filtering.log
```

### 🛠️ Troubleshooting

**No logs appearing?**

- Check `ENABLE_DETAILED_FILTERING_LOGS=true` in your `.env`
- Verify the `logs/` directory was created
- Check LOG_LEVEL setting

**Too many logs?**

- Set `ENABLE_DETAILED_FILTERING_LOGS=false` to disable
- Increase `LOG_LEVEL` to `WARN` or `ERROR`
- Adjust `MAX_LOG_FILE_SIZE` if files get too large

### 📚 Full Documentation

For complete details, see [FILTERING_LOGS_GUIDE.md](./FILTERING_LOGS_GUIDE.md)

---

**Next Steps:**

1. Upload a document to see real filtering in action
2. Run `node analyze-logs.js` to see the analysis
3. Review the logs to understand your document processing patterns
