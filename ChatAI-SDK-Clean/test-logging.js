#!/usr/bin/env node

const logger = require('./src/utils/logger');
const fs = require('fs');
const path = require('path');

/**
 * Test script to verify the logging functionality
 */
class LoggingTester {
  constructor() {
    this.testDocumentInfo = {
      filename: 'test-document.pdf',
      uploadId: 'test-upload-123',
      totalPages: 10,
      fileSize: 1024000
    };
  }

  /**
   * Test page filtering logging
   */
  testPageFilteringLogs() {
    console.log('\n🧪 Testing Page Filtering Logs');
    console.log('==============================');

    // Test filtered page
    logger.logPageFiltering(
      this.testDocumentInfo,
      {
        pageNumber: 3,
        content: 'Chapter 3\n\n',
        wordCount: 2,
        charCount: 12,
        meaningfulRatio: 0.15,
        filterReason: 'Header-only content'
      },
      'FILTERED'
    );

    // Test merged page
    logger.logPageFiltering(
      this.testDocumentInfo,
      {
        pageNumber: 5,
        content: 'Page 5\n\nSome short content here.',
        wordCount: 6,
        charCount: 35,
        meaningfulRatio: 0.3,
        filterReason: 'Too short (6 < 20 words)'
      },
      'MERGED',
      {
        strategy: 'backward',
        mergedWith: 4,
        resultContent: 'Previous page content...\n\nPage 5\n\nSome short content here.',
        resultWordCount: 25
      }
    );

    console.log('✅ Page filtering logs created');
  }

  /**
   * Test chunk filtering logging
   */
  testChunkFilteringLogs() {
    console.log('\n🧪 Testing Chunk Filtering Logs');
    console.log('===============================');

    // Test various filtering reasons
    const testChunks = [
      {
        index: 0,
        pageNumber: 1,
        content: '----------Table Header----------',
        wordCount: 2,
        charCount: 31,
        meaningfulRatio: 0.1,
        filterReason: 'Table formatting artifacts (100% artifact lines)',
        matchedPattern: 'tableArtifactPatterns'
      },
      {
        index: 5,
        pageNumber: 3,
        content: 'Page 3 of 50',
        wordCount: 3,
        charCount: 12,
        meaningfulRatio: 0.8,
        filterReason: 'Navigation/structural element (page number, TOC, etc.)',
        matchedPattern: '/^(page\\s+\\d+(\\s+of\\s+\\d+)?)\\s*$/i'
      },
      {
        index: 12,
        pageNumber: 7,
        content: 'Short',
        wordCount: 1,
        charCount: 5,
        meaningfulRatio: 1.0,
        filterReason: 'Too short (5 < 100 chars)'
      },
      {
        index: 18,
        pageNumber: 9,
        content: 'Copyright 2024 Company Name. All rights reserved.',
        wordCount: 8,
        charCount: 48,
        meaningfulRatio: 0.9,
        filterReason: 'Copyright or legal notice',
        matchedPattern: '/^copyright\\s+\\d{4}/i'
      },
      {
        index: 25,
        pageNumber: 12,
        content: 'the the the the the the the the the the',
        wordCount: 10,
        charCount: 39,
        meaningfulRatio: 0.8,
        repetitionRatio: 0.9,
        filterReason: 'High repetition ratio (90% > 70%)'
      }
    ];

    testChunks.forEach(chunk => {
      logger.logChunkFiltering(this.testDocumentInfo, chunk);
    });

    console.log('✅ Chunk filtering logs created');
  }

  /**
   * Test processing summary logging
   */
  testProcessingSummaryLogs() {
    console.log('\n🧪 Testing Processing Summary Logs');
    console.log('==================================');

    const summary = {
      startTime: new Date(Date.now() - 5000).toISOString(),
      endTime: new Date().toISOString(),
      durationMs: 5000,
      steps: ['parsing', 'page-filtering', 'chunk-filtering', 'embedding', 'vector-storage'],
      pages: {
        original: 10,
        filtered: 2,
        merged: 1,
        final: 8
      },
      chunks: {
        original: 50,
        filtered: 8,
        final: 42
      },
      cost: {
        embeddingsSaved: 8,
        estimatedSavings: '16%'
      }
    };

    logger.logProcessingSummary(this.testDocumentInfo, summary);

    console.log('✅ Processing summary logs created');
  }

  /**
   * Test error and warning logging
   */
  testErrorAndWarningLogs() {
    console.log('\n🧪 Testing Error and Warning Logs');
    console.log('=================================');

    // Test warning
    logger.logWarning(
      'Document Processing',
      'Document has unusually high filtering rate',
      {
        filename: this.testDocumentInfo.filename,
        filteringRate: 0.35,
        threshold: 0.25
      }
    );

    // Test error
    try {
      throw new Error('Simulated processing error for testing');
    } catch (error) {
      logger.logError(
        'Document Processing',
        error,
        {
          filename: this.testDocumentInfo.filename,
          step: 'chunk-filtering',
          chunkIndex: 15
        }
      );
    }

    console.log('✅ Error and warning logs created');
  }

  /**
   * Verify log files were created
   */
  verifyLogFiles() {
    console.log('\n🔍 Verifying Log Files');
    console.log('======================');

    const today = new Date().toISOString().split('T')[0];
    const expectedFiles = [
      `logs/document-processing/page-filtering/${today}_page-filtering.log`,
      `logs/document-processing/chunk-filtering/${today}_chunk-filtering.log`,
      `logs/document-processing/processing-summary/${today}_processing-summary.log`,
      `logs/general/${today}_general.log`
    ];

    expectedFiles.forEach(filePath => {
      const fullPath = path.join(__dirname, filePath);
      if (fs.existsSync(fullPath)) {
        const stats = fs.statSync(fullPath);
        console.log(`✅ ${filePath} (${stats.size} bytes)`);
      } else {
        console.log(`❌ ${filePath} - NOT FOUND`);
      }
    });
  }

  /**
   * Show sample log entries
   */
  showSampleLogEntries() {
    console.log('\n📋 Sample Log Entries');
    console.log('=====================');

    const today = new Date().toISOString().split('T')[0];
    const chunkLogFile = path.join(__dirname, `logs/document-processing/chunk-filtering/${today}_chunk-filtering.log`);
    
    if (fs.existsSync(chunkLogFile)) {
      const content = fs.readFileSync(chunkLogFile, 'utf8');
      const lines = content.trim().split('\n');
      
      if (lines.length > 0) {
        console.log('\n📝 Latest chunk filtering entry:');
        try {
          const entry = JSON.parse(lines[lines.length - 1]);
          console.log(JSON.stringify(entry, null, 2));
        } catch (e) {
          console.log('Error parsing log entry:', e.message);
        }
      }
    }
  }

  /**
   * Test log statistics
   */
  testLogStatistics() {
    console.log('\n📊 Testing Log Statistics');
    console.log('=========================');

    const categories = ['page-filtering', 'chunk-filtering', 'processing-summary', 'general'];
    
    categories.forEach(category => {
      const stats = logger.getLogStats(category, 1); // Just today
      console.log(`\n${category}:`);
      console.log(`  Total entries: ${stats.totalEntries}`);
      console.log(`  File size: ${stats.fileSize} bytes`);
      console.log(`  Types: ${Object.keys(stats.byType).join(', ')}`);
    });
  }

  /**
   * Run all tests
   */
  runAllTests() {
    console.log('🧪 LOGGING SYSTEM TEST');
    console.log('======================');
    console.log('This script will test all logging functionality...\n');

    this.testPageFilteringLogs();
    this.testChunkFilteringLogs();
    this.testProcessingSummaryLogs();
    this.testErrorAndWarningLogs();
    
    // Give a moment for files to be written
    setTimeout(() => {
      this.verifyLogFiles();
      this.showSampleLogEntries();
      this.testLogStatistics();
      
      console.log('\n🎉 Logging system test completed!');
      console.log('\n💡 Next steps:');
      console.log('   1. Upload a document to see real filtering logs');
      console.log('   2. Run: node analyze-logs.js');
      console.log('   3. Check logs directory for detailed filtering data');
    }, 100);
  }
}

// CLI interface
if (require.main === module) {
  const tester = new LoggingTester();
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    tester.runAllTests();
  } else {
    const command = args[0];
    
    switch (command) {
      case 'pages':
        tester.testPageFilteringLogs();
        break;
      case 'chunks':
        tester.testChunkFilteringLogs();
        break;
      case 'summary':
        tester.testProcessingSummaryLogs();
        break;
      case 'errors':
        tester.testErrorAndWarningLogs();
        break;
      case 'verify':
        tester.verifyLogFiles();
        break;
      case 'stats':
        tester.testLogStatistics();
        break;
      default:
        console.log('Usage: node test-logging.js [pages|chunks|summary|errors|verify|stats]');
        console.log('       node test-logging.js                    # Run all tests');
    }
  }
}

module.exports = LoggingTester;
