/**
 * Test script to verify the enhanced valuable content detection
 */

// Import the function we want to test
const fs = require('fs');
const path = require('path');

// Read the vectorProcessing.js file to extract the function
const vectorProcessingPath = path.join(__dirname, 'src', 'routes', 'vectorProcessing.js');
const vectorProcessingCode = fs.readFileSync(vectorProcessingPath, 'utf8');

// Extract the detectValuableShortContent function
const functionMatch = vectorProcessingCode.match(/function detectValuableShortContent\([\s\S]*?\n}/);
if (!functionMatch) {
  console.error('❌ Could not find detectValuableShortContent function');
  process.exit(1);
}

// Create a test environment
eval(functionMatch[0]);

// Test cases from the actual filtered content
const testCases = [
  {
    content: "Large             Sampling for LLM Generation Language Models...",
    expected: 'technicalTopic',
    description: 'LLM Generation content'
  },
  {
    content: "Large             Pretraining Large Language Language           Models: Algorith...",
    expected: 'technicalTopic',
    description: 'Pretraining content'
  },
  {
    content: "Large              Pretraining data for LLMs Language Models...",
    expected: 'technicalTopic',
    description: 'Pretraining data content'
  },
  {
    content: "Large            Finetuning Language Models...",
    expected: 'technicalTopic',
    description: 'Finetuning content'
  },
  {
    content: "Large            Evaluating Large Language Language          Models Models...",
    expected: 'technicalTopic',
    description: 'Evaluating content'
  },
  {
    content: "Misinformation  Chatbots are generating false and  misleading information about...",
    expected: 'safetyTopic',
    description: 'Misinformation content'
  },
  {
    content: "Large            Harms of Large Language Language         Models Models...",
    expected: 'safetyTopic',
    description: 'Harms content'
  },
  // Additional test cases
  {
    content: "Introduction to Neural Networks",
    expected: 'sectionHeader',
    description: 'Introduction section'
  },
  {
    content: "Methodology and Approach",
    expected: 'sectionHeader',
    description: 'Methodology section'
  },
  {
    content: "Results and Discussion",
    expected: 'sectionHeader',
    description: 'Results section'
  }
];

// Mock criteria object
const mockCriteria = {
  valuableShortPatterns: [], // Not used in our updated function
  specialContentTypes: {
    mathematical: { minWords: 5, minChars: 30 },
    definition: { minWords: 8, minChars: 50 },
    keyPoint: { minWords: 10, minChars: 80 },
    citation: { minWords: 3, minChars: 20 },
    technicalTopic: { minWords: 3, minChars: 15 },
    safetyTopic: { minWords: 3, minChars: 15 },
    sectionHeader: { minWords: 3, minChars: 20 }
  }
};

console.log('🧪 Testing Enhanced Valuable Content Detection');
console.log('==============================================\n');

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const result = detectValuableShortContent(testCase.content, mockCriteria);
  const success = result === testCase.expected;
  
  if (success) {
    console.log(`✅ Test ${index + 1}: ${testCase.description}`);
    console.log(`   Content: "${testCase.content.substring(0, 50)}..."`);
    console.log(`   Expected: ${testCase.expected}, Got: ${result}\n`);
    passed++;
  } else {
    console.log(`❌ Test ${index + 1}: ${testCase.description}`);
    console.log(`   Content: "${testCase.content.substring(0, 50)}..."`);
    console.log(`   Expected: ${testCase.expected}, Got: ${result}\n`);
    failed++;
  }
});

console.log('📊 Test Results:');
console.log(`✅ Passed: ${passed}/${testCases.length}`);
console.log(`❌ Failed: ${failed}/${testCases.length}`);
console.log(`🎯 Success Rate: ${Math.round((passed / testCases.length) * 100)}%`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! The enhanced detection should work correctly.');
} else {
  console.log('\n⚠️ Some tests failed. The detection patterns may need adjustment.');
}
