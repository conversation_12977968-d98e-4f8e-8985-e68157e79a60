#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const logger = require('./src/utils/logger');

/**
 * Simple log analysis script to help understand filtering patterns
 */
class LogAnalyzer {
  constructor() {
    this.logsDir = path.join(__dirname, 'logs');
  }

  /**
   * Analyze page filtering logs
   */
  analyzePageFiltering(days = 7) {
    console.log('\n📄 PAGE FILTERING ANALYSIS');
    console.log('==========================');

    const stats = logger.getLogStats('page-filtering', days);
    
    if (stats.totalEntries === 0) {
      console.log('No page filtering logs found.');
      return;
    }

    console.log(`📊 Total entries: ${stats.totalEntries}`);
    console.log(`📁 Log file size: ${(stats.fileSize / 1024 / 1024).toFixed(2)} MB`);
    console.log('\n📈 Entries by type:');
    Object.entries(stats.byType).forEach(([type, count]) => {
      console.log(`   ${type}: ${count}`);
    });

    console.log('\n📅 Entries by day:');
    Object.entries(stats.byDay).forEach(([day, count]) => {
      console.log(`   ${day}: ${count}`);
    });

    // Analyze specific filtering patterns
    this.analyzePageFilteringPatterns(days);
  }

  /**
   * Analyze chunk filtering logs
   */
  analyzeChunkFiltering(days = 7) {
    console.log('\n📝 CHUNK FILTERING ANALYSIS');
    console.log('===========================');

    const stats = logger.getLogStats('chunk-filtering', days);
    
    if (stats.totalEntries === 0) {
      console.log('No chunk filtering logs found.');
      return;
    }

    console.log(`📊 Total entries: ${stats.totalEntries}`);
    console.log(`📁 Log file size: ${(stats.fileSize / 1024 / 1024).toFixed(2)} MB`);
    console.log('\n📈 Entries by type:');
    Object.entries(stats.byType).forEach(([type, count]) => {
      console.log(`   ${type}: ${count}`);
    });

    // Analyze specific filtering patterns
    this.analyzeChunkFilteringPatterns(days);
  }

  /**
   * Analyze processing summary logs
   */
  analyzeProcessingSummary(days = 7) {
    console.log('\n📊 PROCESSING SUMMARY ANALYSIS');
    console.log('==============================');

    const stats = logger.getLogStats('processing-summary', days);
    
    if (stats.totalEntries === 0) {
      console.log('No processing summary logs found.');
      return;
    }

    console.log(`📊 Total documents processed: ${stats.totalEntries}`);
    console.log(`📁 Log file size: ${(stats.fileSize / 1024 / 1024).toFixed(2)} MB`);

    // Analyze processing performance
    this.analyzeProcessingPerformance(days);
  }

  /**
   * Analyze page filtering patterns in detail
   */
  analyzePageFilteringPatterns(days = 7) {
    const patterns = {
      'Too short': 0,
      'Header-only content': 0,
      'Footer-only content': 0,
      'Low meaningful content': 0,
      'Merged': 0
    };

    const documents = new Set();

    try {
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const filename = logger.getLogFileName('page-filtering', date);
        
        if (fs.existsSync(filename)) {
          const content = fs.readFileSync(filename, 'utf8');
          const lines = content.trim().split('\n').filter(line => line.trim());
          
          lines.forEach(line => {
            try {
              const entry = JSON.parse(line);
              if (entry.type === 'PAGE_FILTERING') {
                documents.add(entry.document.filename);
                
                const reason = entry.page.filterReason;
                Object.keys(patterns).forEach(pattern => {
                  if (reason.includes(pattern) || reason.startsWith(pattern)) {
                    patterns[pattern]++;
                  }
                });
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          });
        }
      }

      console.log('\n🔍 Page filtering reasons:');
      Object.entries(patterns).forEach(([reason, count]) => {
        if (count > 0) {
          console.log(`   ${reason}: ${count} pages`);
        }
      });

      console.log(`\n📄 Documents processed: ${documents.size}`);
    } catch (error) {
      console.error('Error analyzing page filtering patterns:', error.message);
    }
  }

  /**
   * Analyze chunk filtering patterns in detail
   */
  analyzeChunkFilteringPatterns(days = 7) {
    const patterns = {
      'Empty or whitespace': 0,
      'Table formatting artifacts': 0,
      'Navigation/structural element': 0,
      'Copyright or legal notice': 0,
      'Too short': 0,
      'Low word count': 0,
      'Low meaningful content ratio': 0,
      'High repetition ratio': 0
    };

    const documents = new Set();
    let totalCharsFiltered = 0;
    let totalWordsFiltered = 0;

    try {
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const filename = logger.getLogFileName('chunk-filtering', date);
        
        if (fs.existsSync(filename)) {
          const content = fs.readFileSync(filename, 'utf8');
          const lines = content.trim().split('\n').filter(line => line.trim());
          
          lines.forEach(line => {
            try {
              const entry = JSON.parse(line);
              if (entry.type === 'CHUNK_FILTERING') {
                documents.add(entry.document.filename);
                
                const reason = entry.chunk.filterReason;
                totalCharsFiltered += entry.chunk.charCount || 0;
                totalWordsFiltered += entry.chunk.wordCount || 0;
                
                Object.keys(patterns).forEach(pattern => {
                  if (reason.includes(pattern) || reason.startsWith(pattern)) {
                    patterns[pattern]++;
                  }
                });
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          });
        }
      }

      console.log('\n🔍 Chunk filtering reasons:');
      Object.entries(patterns).forEach(([reason, count]) => {
        if (count > 0) {
          console.log(`   ${reason}: ${count} chunks`);
        }
      });

      console.log(`\n📊 Content filtered:`);
      console.log(`   Characters: ${totalCharsFiltered.toLocaleString()}`);
      console.log(`   Words: ${totalWordsFiltered.toLocaleString()}`);
      console.log(`   Documents: ${documents.size}`);
    } catch (error) {
      console.error('Error analyzing chunk filtering patterns:', error.message);
    }
  }

  /**
   * Analyze processing performance
   */
  analyzeProcessingPerformance(days = 7) {
    const performance = {
      totalDocuments: 0,
      totalDuration: 0,
      avgDuration: 0,
      totalPagesSaved: 0,
      totalChunksSaved: 0,
      avgPageSavings: 0,
      avgChunkSavings: 0
    };

    try {
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const filename = logger.getLogFileName('processing-summary', date);
        
        if (fs.existsSync(filename)) {
          const content = fs.readFileSync(filename, 'utf8');
          const lines = content.trim().split('\n').filter(line => line.trim());
          
          lines.forEach(line => {
            try {
              const entry = JSON.parse(line);
              if (entry.type === 'PROCESSING_SUMMARY') {
                performance.totalDocuments++;
                performance.totalDuration += entry.processing.durationMs;
                performance.totalPagesSaved += entry.pages.filtered;
                performance.totalChunksSaved += entry.chunks.filtered;
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          });
        }
      }

      if (performance.totalDocuments > 0) {
        performance.avgDuration = Math.round(performance.totalDuration / performance.totalDocuments);
        performance.avgPageSavings = Math.round(performance.totalPagesSaved / performance.totalDocuments);
        performance.avgChunkSavings = Math.round(performance.totalChunksSaved / performance.totalDocuments);

        console.log('\n⚡ Processing performance:');
        console.log(`   Documents processed: ${performance.totalDocuments}`);
        console.log(`   Average duration: ${performance.avgDuration}ms`);
        console.log(`   Total pages filtered: ${performance.totalPagesSaved}`);
        console.log(`   Total chunks filtered: ${performance.totalChunksSaved}`);
        console.log(`   Avg pages saved per doc: ${performance.avgPageSavings}`);
        console.log(`   Avg chunks saved per doc: ${performance.avgChunkSavings}`);
      }
    } catch (error) {
      console.error('Error analyzing processing performance:', error.message);
    }
  }

  /**
   * Show recent filtered content examples
   */
  showRecentExamples(type = 'chunk', limit = 5) {
    console.log(`\n📋 RECENT ${type.toUpperCase()} FILTERING EXAMPLES`);
    console.log('='.repeat(40));

    const category = type === 'chunk' ? 'chunk-filtering' : 'page-filtering';
    const filename = logger.getLogFileName(category);
    
    if (!fs.existsSync(filename)) {
      console.log(`No ${type} filtering logs found for today.`);
      return;
    }

    try {
      const content = fs.readFileSync(filename, 'utf8');
      const lines = content.trim().split('\n').filter(line => line.trim());
      const recentEntries = lines.slice(-limit);

      recentEntries.forEach((line, index) => {
        try {
          const entry = JSON.parse(line);
          const timestamp = new Date(entry.timestamp).toLocaleTimeString();
          
          if (type === 'chunk' && entry.type === 'CHUNK_FILTERING') {
            console.log(`\n${index + 1}. [${timestamp}] ${entry.document.filename}`);
            console.log(`   Reason: ${entry.chunk.filterReason}`);
            console.log(`   Content: "${entry.chunk.content.substring(0, 100)}..."`);
            console.log(`   Stats: ${entry.chunk.wordCount} words, ${entry.chunk.charCount} chars`);
          } else if (type === 'page' && entry.type === 'PAGE_FILTERING') {
            console.log(`\n${index + 1}. [${timestamp}] ${entry.document.filename}`);
            console.log(`   Page: ${entry.page.pageNumber}`);
            console.log(`   Reason: ${entry.page.filterReason}`);
            console.log(`   Action: ${entry.action}`);
            console.log(`   Stats: ${entry.page.wordCount} words, ${entry.page.charCount} chars`);
          }
        } catch (e) {
          // Skip invalid JSON lines
        }
      });
    } catch (error) {
      console.error(`Error showing recent ${type} examples:`, error.message);
    }
  }

  /**
   * Run complete analysis
   */
  runCompleteAnalysis(days = 7) {
    console.log('🔍 DOCUMENT FILTERING LOG ANALYSIS');
    console.log('==================================');
    console.log(`Analyzing logs from the last ${days} days...\n`);

    this.analyzePageFiltering(days);
    this.analyzeChunkFiltering(days);
    this.analyzeProcessingSummary(days);
    
    console.log('\n📋 RECENT EXAMPLES');
    console.log('==================');
    this.showRecentExamples('chunk', 3);
    this.showRecentExamples('page', 3);
  }
}

// CLI interface
if (require.main === module) {
  const analyzer = new LogAnalyzer();
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    analyzer.runCompleteAnalysis();
  } else {
    const command = args[0];
    const days = parseInt(args[1]) || 7;
    
    switch (command) {
      case 'pages':
        analyzer.analyzePageFiltering(days);
        break;
      case 'chunks':
        analyzer.analyzeChunkFiltering(days);
        break;
      case 'summary':
        analyzer.analyzeProcessingSummary(days);
        break;
      case 'examples':
        const type = args[1] || 'chunk';
        const limit = parseInt(args[2]) || 5;
        analyzer.showRecentExamples(type, limit);
        break;
      default:
        console.log('Usage: node analyze-logs.js [pages|chunks|summary|examples] [days|type] [limit]');
        console.log('Examples:');
        console.log('  node analyze-logs.js                    # Complete analysis');
        console.log('  node analyze-logs.js pages 7            # Page filtering for 7 days');
        console.log('  node analyze-logs.js chunks 3           # Chunk filtering for 3 days');
        console.log('  node analyze-logs.js examples chunk 10  # 10 recent chunk examples');
    }
  }
}

module.exports = LogAnalyzer;
