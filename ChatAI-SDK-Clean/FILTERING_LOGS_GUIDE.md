# Document Filtering Logs Guide

This guide explains the comprehensive logging system that tracks all data filtered during document processing.

## Overview

The logging system captures detailed information about:
- **Page Filtering**: Pages removed or merged during document processing
- **Chunk Filtering**: Text chunks filtered out as low-quality or junk content
- **Processing Summary**: Overall document processing metrics and performance

## Log Structure

### Directory Structure
```
logs/
├── document-processing/
│   ├── page-filtering/
│   │   └── YYYY-MM-DD_page-filtering.log
│   ├── chunk-filtering/
│   │   └── YYYY-MM-DD_chunk-filtering.log
│   └── processing-summary/
│       └── YYYY-MM-DD_processing-summary.log
└── general/
    └── YYYY-MM-DD_general.log
```

### Log Format
All logs use structured JSON format for easy parsing and analysis:

```json
{
  "timestamp": "2025-01-15T10:30:00.000Z",
  "type": "PAGE_FILTERING",
  "level": "INFO",
  "document": {
    "filename": "example.pdf",
    "uploadId": "uuid-here"
  },
  "page": {
    "pageNumber": 5,
    "originalContent": "Chapter 5\n\n",
    "wordCount": 2,
    "charCount": 12,
    "meaningfulRatio": 0.1,
    "filterReason": "headerOnly"
  },
  "action": "FILTERED"
}
```

## Page Filtering Logs

### What Gets Logged
- Pages filtered out due to insufficient content
- Pages merged with adjacent pages
- Filtering criteria and reasons
- Original page content (truncated for readability)

### Filtering Reasons
- **Too short**: Less than 20 words or 150 characters
- **Header-only content**: Contains only chapter/section headers
- **Footer-only content**: Contains only page numbers or copyright notices
- **Low meaningful content**: Less than 40% meaningful characters

### Example Entry
```json
{
  "timestamp": "2025-01-15T10:30:00.000Z",
  "type": "PAGE_FILTERING",
  "level": "INFO",
  "document": {
    "filename": "research-paper.pdf",
    "uploadId": "doc-123",
    "totalPages": 25
  },
  "page": {
    "pageNumber": 3,
    "originalContent": "Chapter 3: Introduction\n\n",
    "wordCount": 3,
    "charCount": 25,
    "meaningfulRatio": 0.72,
    "filterReason": "Too short (3 < 20 words)"
  },
  "action": "FILTERED"
}
```

## Chunk Filtering Logs

### What Gets Logged
- Text chunks filtered as junk or low-quality content
- Filtering criteria that triggered removal
- Content statistics and quality metrics

### Filtering Reasons
- **Empty or whitespace-only content**
- **Table formatting artifacts**: Lines of dashes, borders, separators
- **Navigation/structural elements**: Page numbers, TOC entries
- **Copyright or legal notices**
- **Too short**: Less than 100 characters or 15 words
- **Low meaningful content ratio**: Less than 30% meaningful characters
- **High repetition ratio**: More than 70% repeated content

### Example Entry
```json
{
  "timestamp": "2025-01-15T10:31:00.000Z",
  "type": "CHUNK_FILTERING",
  "level": "INFO",
  "document": {
    "filename": "research-paper.pdf",
    "uploadId": "doc-123"
  },
  "chunk": {
    "index": 15,
    "pageNumber": 5,
    "content": "----------Table----------\n| Col1 | Col2 |\n----------",
    "wordCount": 4,
    "charCount": 45,
    "meaningfulRatio": 0.2,
    "filterReason": "Table formatting artifacts (80% artifact lines)",
    "matchedPattern": "tableArtifactPatterns"
  }
}
```

## Processing Summary Logs

### What Gets Logged
- Overall document processing metrics
- Time taken for each processing step
- Cost savings from filtering
- Success rates and performance data

### Example Entry
```json
{
  "timestamp": "2025-01-15T10:32:00.000Z",
  "type": "PROCESSING_SUMMARY",
  "level": "INFO",
  "document": {
    "filename": "research-paper.pdf",
    "uploadId": "doc-123",
    "fileSize": 2048000
  },
  "processing": {
    "startTime": "2025-01-15T10:30:00.000Z",
    "endTime": "2025-01-15T10:32:00.000Z",
    "durationMs": 120000,
    "steps": ["parsing", "page-filtering", "chunk-filtering", "embedding", "vector-storage"]
  },
  "pages": {
    "original": 25,
    "filtered": 3,
    "merged": 2,
    "final": 22,
    "savingsPercent": 12
  },
  "chunks": {
    "original": 150,
    "filtered": 25,
    "final": 125,
    "savingsPercent": 17
  },
  "cost": {
    "embeddingsSaved": 25,
    "estimatedSavings": "17%"
  }
}
```

## Configuration

### Environment Variables
```bash
# Enable/disable detailed filtering logs
ENABLE_DETAILED_FILTERING_LOGS=true

# Set log level (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# Maximum log file size before rotation (bytes)
MAX_LOG_FILE_SIZE=10485760
```

### Log Levels
- **DEBUG**: All logging including verbose details
- **INFO**: Standard logging (default)
- **WARN**: Warnings and errors only
- **ERROR**: Errors only

## Using the Logs

### 1. Real-time Monitoring
Watch logs in real-time during document processing:
```bash
tail -f logs/document-processing/chunk-filtering/$(date +%Y-%m-%d)_chunk-filtering.log
```

### 2. Log Analysis
Use the built-in analysis script:
```bash
# Complete analysis
node analyze-logs.js

# Specific analysis
node analyze-logs.js pages 7        # Page filtering for 7 days
node analyze-logs.js chunks 3       # Chunk filtering for 3 days
node analyze-logs.js examples chunk 10  # Recent chunk examples
```

### 3. Custom Analysis
Parse JSON logs with your preferred tools:
```bash
# Count filtering reasons
cat logs/document-processing/chunk-filtering/*.log | \
  jq -r '.chunk.filterReason' | \
  sort | uniq -c | sort -nr

# Calculate average processing time
cat logs/document-processing/processing-summary/*.log | \
  jq -r '.processing.durationMs' | \
  awk '{sum+=$1; count++} END {print "Average:", sum/count, "ms"}'
```

## Testing the Logging System

### 1. Run Test Script
```bash
node test-logging.js
```

### 2. Upload a Document
Upload any document through the API to see real filtering logs.

### 3. Analyze Results
```bash
node analyze-logs.js
```

## Benefits

### 1. **Transparency**
- See exactly what content gets filtered and why
- Understand the impact of filtering criteria
- Track processing performance over time

### 2. **Optimization**
- Identify patterns in filtered content
- Adjust filtering criteria based on real data
- Monitor cost savings from filtering

### 3. **Debugging**
- Troubleshoot processing issues
- Understand why certain content isn't searchable
- Validate filtering logic effectiveness

### 4. **Monitoring**
- Track system performance
- Monitor processing success rates
- Identify documents with unusual filtering patterns

## Log Rotation

- Logs are automatically rotated daily
- Files larger than MAX_LOG_FILE_SIZE are rotated with timestamp suffix
- Old log files are preserved for historical analysis

## Security

- Log files are excluded from version control (.gitignore)
- Sensitive content is truncated in logs
- No API keys or credentials are logged
- Personal information is sanitized

## Troubleshooting

### No Logs Generated
1. Check `ENABLE_DETAILED_FILTERING_LOGS=true` in environment
2. Verify log directory permissions
3. Check LOG_LEVEL setting

### Large Log Files
1. Adjust MAX_LOG_FILE_SIZE if needed
2. Implement log cleanup scripts for old files
3. Consider log aggregation for production

### Performance Impact
1. Logging is designed to be lightweight
2. File I/O is asynchronous where possible
3. Disable detailed logs in production if needed

## Next Steps

1. **Monitor Initial Usage**: Watch logs during first few document uploads
2. **Analyze Patterns**: Use analysis tools to understand filtering effectiveness
3. **Optimize Criteria**: Adjust filtering rules based on log analysis
4. **Set Up Monitoring**: Create alerts for unusual filtering patterns
5. **Archive Old Logs**: Implement log retention policies as needed
