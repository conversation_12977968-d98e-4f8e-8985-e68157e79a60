/**
 * Test script to verify that valuable short pages are now being preserved
 */

const fs = require('fs');
const path = require('path');

// Create test pages that should be preserved with our enhanced detection
const testPages = [
  {
    pageNumber: 27,
    text: "Large             Sampling for LLM Generation Language Models...",
    expectedResult: 'PRESERVED',
    expectedType: 'technicalTopic'
  },
  {
    pageNumber: 35,
    text: "Large             Pretraining Large Language Language           Models: Algorith...",
    expectedResult: 'PRESERVED',
    expectedType: 'technicalTopic'
  },
  {
    pageNumber: 43,
    text: "Large              Pretraining data for LLMs Language Models...",
    expectedResult: 'PRESERVED',
    expectedType: 'technicalTopic'
  },
  {
    pageNumber: 49,
    text: "Large            Finetuning Language Models...",
    expectedResult: 'PRESERVED',
    expectedType: 'technicalTopic'
  },
  {
    pageNumber: 50,
    text: "Large            Evaluating Large Language Language          Models Models...",
    expectedResult: 'PRESERVED',
    expectedType: 'technicalTopic'
  },
  {
    pageNumber: 71,
    text: "Misinformation  Chatbots are generating false and  misleading information about...",
    expectedResult: 'PRESERVED',
    expectedType: 'safetyTopic'
  },
  {
    pageNumber: 72,
    text: "Large            Harms of Large Language Language         Models Models...",
    expectedResult: 'PRESERVED',
    expectedType: 'safetyTopic'
  }
];

// Import the filtering function
const vectorProcessingPath = path.join(__dirname, 'src', 'routes', 'vectorProcessing.js');
const vectorProcessingCode = fs.readFileSync(vectorProcessingPath, 'utf8');

// Extract the functions we need
const detectValuableMatch = vectorProcessingCode.match(/function detectValuableShortContent\([\s\S]*?\n}/);
const filterAndMergeMatch = vectorProcessingCode.match(/function filterAndMergeShortPages\([\s\S]*?\n  return \{[\s\S]*?\};\n}/);

if (!detectValuableMatch || !filterAndMergeMatch) {
  console.error('❌ Could not find required functions');
  process.exit(1);
}

// Helper functions
function countWords(text) {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

// Create test environment
eval(detectValuableMatch[0]);
eval(filterAndMergeMatch[0]);

console.log('🧪 Testing Short Page Preservation');
console.log('==================================\n');

// Convert test data to the format expected by filterAndMergeShortPages
const mockPages = testPages.map(page => ({
  pageNumber: page.pageNumber,
  text: page.text,
  markdown: page.text
}));

// Test the filtering function
const { filteredPages, pageFilterReport } = filterAndMergeShortPages(mockPages, {
  filename: 'test-document.pdf',
  uploadId: 'test-123'
});

console.log('📊 Test Results:');
console.log('================');
console.log(`📄 Original pages: ${mockPages.length}`);
console.log(`✅ Pages preserved: ${filteredPages.length}`);
console.log(`🗑️  Pages filtered: ${pageFilterReport.filtered.length}`);
console.log(`🔗 Pages merged: ${pageFilterReport.merged.length}`);

console.log('\n📋 Detailed Results:');
console.log('====================');

let preserved = 0;
let filtered = 0;

testPages.forEach((testPage, index) => {
  const wasPreserved = filteredPages.some(page => page.pageNumber === testPage.pageNumber);
  const wasFiltered = pageFilterReport.filtered.some(page => page.pageNumber === testPage.pageNumber);
  
  if (wasPreserved) {
    console.log(`✅ Page ${testPage.pageNumber}: PRESERVED (${testPage.expectedType})`);
    console.log(`   Content: "${testPage.text.substring(0, 50)}..."`);
    preserved++;
  } else if (wasFiltered) {
    console.log(`❌ Page ${testPage.pageNumber}: FILTERED`);
    console.log(`   Content: "${testPage.text.substring(0, 50)}..."`);
    console.log(`   Reason: ${pageFilterReport.filtered.find(p => p.pageNumber === testPage.pageNumber)?.reason || 'Unknown'}`);
    filtered++;
  } else {
    console.log(`🔗 Page ${testPage.pageNumber}: MERGED`);
    console.log(`   Content: "${testPage.text.substring(0, 50)}..."`);
  }
  console.log('');
});

console.log('📊 Summary:');
console.log(`✅ Preserved: ${preserved}/${testPages.length} (${Math.round((preserved / testPages.length) * 100)}%)`);
console.log(`❌ Filtered: ${filtered}/${testPages.length} (${Math.round((filtered / testPages.length) * 100)}%)`);
console.log(`🔗 Merged: ${testPages.length - preserved - filtered}/${testPages.length}`);

if (preserved === testPages.length) {
  console.log('\n🎉 SUCCESS: All valuable short pages are now being preserved!');
} else if (preserved > 0) {
  console.log('\n⚠️ PARTIAL SUCCESS: Some valuable pages are being preserved, but improvements needed.');
} else {
  console.log('\n❌ FAILURE: Valuable pages are still being filtered out.');
}

// Show filter report details
if (pageFilterReport.filtered.length > 0) {
  console.log('\n🗑️ Filtered Pages Details:');
  pageFilterReport.filtered.forEach(page => {
    console.log(`   Page ${page.pageNumber}: ${page.reason} (${page.words} words, ${page.chars} chars)`);
  });
}

if (pageFilterReport.merged.length > 0) {
  console.log('\n🔗 Merged Pages Details:');
  pageFilterReport.merged.forEach(merge => {
    console.log(`   Pages ${merge.mergedPages.join(' + ')}: ${merge.originalWords} → ${merge.mergedWords} words`);
  });
}
