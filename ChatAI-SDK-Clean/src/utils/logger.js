const fs = require('fs');
const path = require('path');

class DocumentProcessingLogger {
  constructor() {
    this.logsDir = path.join(__dirname, '../../logs');
    this.enableDetailedLogs = process.env.ENABLE_DETAILED_FILTERING_LOGS !== 'false'; // Default to true
    this.logLevel = process.env.LOG_LEVEL || 'INFO';
    this.maxLogFileSize = parseInt(process.env.MAX_LOG_FILE_SIZE) || 10 * 1024 * 1024; // 10MB default

    this.initializeLogDirectories();
  }

  initializeLogDirectories() {
    const directories = [
      this.logsDir,
      path.join(this.logsDir, 'document-processing'),
      path.join(this.logsDir, 'document-processing', 'page-filtering'),
      path.join(this.logsDir, 'document-processing', 'chunk-filtering'),
      path.join(this.logsDir, 'document-processing', 'processing-summary'),
      path.join(this.logsDir, 'general')
    ];

    directories.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  getLogFileName(category, date = new Date()) {
    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD
    const categoryPath = {
      'page-filtering': 'document-processing/page-filtering',
      'chunk-filtering': 'document-processing/chunk-filtering',
      'processing-summary': 'document-processing/processing-summary',
      'general': 'general'
    };

    const subDir = categoryPath[category] || 'general';
    return path.join(this.logsDir, subDir, `${dateStr}_${category}.log`);
  }

  shouldLog(level) {
    const levels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
    return levels[level] >= levels[this.logLevel];
  }

  writeToFile(filename, logEntry) {
    try {
      // Check file size and rotate if necessary
      if (fs.existsSync(filename)) {
        const stats = fs.statSync(filename);
        if (stats.size > this.maxLogFileSize) {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const rotatedName = filename.replace('.log', `_${timestamp}.log`);
          fs.renameSync(filename, rotatedName);
        }
      }

      const logLine = JSON.stringify(logEntry) + '\n';
      fs.appendFileSync(filename, logLine);
    } catch (error) {
      console.error('❌ Failed to write to log file:', error.message);
    }
  }

  logPageFiltering(documentInfo, pageInfo, action = 'FILTERED', mergeInfo = null) {
    if (!this.enableDetailedLogs || !this.shouldLog('INFO')) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      type: 'PAGE_FILTERING',
      level: 'INFO',
      document: {
        filename: documentInfo.filename,
        uploadId: documentInfo.uploadId || 'unknown',
        totalPages: documentInfo.totalPages
      },
      page: {
        pageNumber: pageInfo.pageNumber,
        originalContent: this.truncateContent(pageInfo.content, 500),
        wordCount: pageInfo.wordCount,
        charCount: pageInfo.charCount,
        meaningfulRatio: Math.round((pageInfo.meaningfulRatio || 0) * 100) / 100,
        filterReason: pageInfo.filterReason,
        matchedPattern: pageInfo.matchedPattern
      },
      action: action,
      mergeInfo: mergeInfo
    };

    const filename = this.getLogFileName('page-filtering');
    this.writeToFile(filename, logEntry);

    // Also log to console for immediate feedback
    if (action === 'FILTERED') {
      const preview = this.truncateContent(pageInfo.content, 50).replace(/\n/g, ' ');
      console.log(`📄 Filtered page ${pageInfo.pageNumber}: ${pageInfo.filterReason}`);
      console.log(`   📊 ${pageInfo.wordCount} words, ${pageInfo.charCount} chars`);
      console.log(`   📝 "${preview}"`);
    } else if (action === 'MERGED') {
      console.log(`🔗 Merged page ${pageInfo.pageNumber} with page ${mergeInfo?.mergedWith} (${mergeInfo?.strategy})`);
      console.log(`   📊 ${pageInfo.wordCount} → ${mergeInfo?.resultWordCount || 'N/A'} words after merge`);
    } else if (action === 'PRESERVED') {
      const preview = this.truncateContent(pageInfo.content, 50).replace(/\n/g, ' ');
      console.log(`📌 Preserved page ${pageInfo.pageNumber}: ${pageInfo.filterReason}`);
      console.log(`   📊 ${pageInfo.wordCount} words, ${pageInfo.charCount} chars`);
      console.log(`   📝 "${preview}"`);
    }
  }

  logChunkFiltering(documentInfo, chunkInfo) {
    if (!this.enableDetailedLogs || !this.shouldLog('INFO')) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      type: 'CHUNK_FILTERING',
      level: 'INFO',
      document: {
        filename: documentInfo.filename,
        uploadId: documentInfo.uploadId || 'unknown'
      },
      chunk: {
        index: chunkInfo.index,
        pageNumber: chunkInfo.pageNumber,
        content: this.truncateContent(chunkInfo.content, 300),
        wordCount: chunkInfo.wordCount,
        charCount: chunkInfo.charCount,
        meaningfulRatio: Math.round((chunkInfo.meaningfulRatio || 0) * 100) / 100,
        repetitionRatio: Math.round((chunkInfo.repetitionRatio || 0) * 100) / 100,
        filterReason: chunkInfo.filterReason,
        matchedPattern: chunkInfo.matchedPattern
      }
    };

    const filename = this.getLogFileName('chunk-filtering');
    this.writeToFile(filename, logEntry);

    // Also log to console for immediate feedback
    const preview = this.truncateContent(chunkInfo.content, 40).replace(/\n/g, ' ');
    console.log(`🗑️  Filtered chunk ${chunkInfo.index}: ${chunkInfo.filterReason}`);
    console.log(`   📊 ${chunkInfo.wordCount} words, ${chunkInfo.charCount} chars`);
    console.log(`   📝 "${preview}"`);
  }

  logProcessingSummary(documentInfo, summary) {
    if (!this.shouldLog('INFO')) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      type: 'PROCESSING_SUMMARY',
      level: 'INFO',
      document: {
        filename: documentInfo.filename,
        uploadId: documentInfo.uploadId || 'unknown',
        fileSize: documentInfo.fileSize
      },
      processing: {
        startTime: summary.startTime,
        endTime: summary.endTime,
        durationMs: summary.durationMs,
        steps: summary.steps
      },
      pages: {
        original: summary.pages.original,
        filtered: summary.pages.filtered,
        merged: summary.pages.merged,
        final: summary.pages.final,
        savingsPercent: Math.round((summary.pages.filtered / summary.pages.original) * 100)
      },
      chunks: {
        original: summary.chunks.original,
        filtered: summary.chunks.filtered,
        final: summary.chunks.final,
        savingsPercent: Math.round((summary.chunks.filtered / summary.chunks.original) * 100)
      },
      cost: {
        embeddingsSaved: summary.cost.embeddingsSaved,
        estimatedSavings: summary.cost.estimatedSavings
      }
    };

    const filename = this.getLogFileName('processing-summary');
    this.writeToFile(filename, logEntry);

    // Enhanced console summary
    console.log('\n📊 Document Processing Summary:');
    console.log(`   📄 File: ${documentInfo.filename}`);
    console.log(`   ⏱️  Duration: ${summary.durationMs}ms`);
    console.log(`   📄 Pages: ${summary.pages.original} → ${summary.pages.final} (${summary.pages.filtered} filtered, ${summary.pages.merged} merged)`);
    console.log(`   📝 Chunks: ${summary.chunks.original} → ${summary.chunks.final} (${summary.chunks.filtered} filtered)`);
    console.log(`   💰 Embeddings saved: ${summary.chunks.filtered} (${Math.round((summary.chunks.filtered / summary.chunks.original) * 100)}%)`);
  }

  logError(context, error, additionalInfo = {}) {
    if (!this.shouldLog('ERROR')) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      type: 'ERROR',
      level: 'ERROR',
      context: context,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      additionalInfo: additionalInfo
    };

    const filename = this.getLogFileName('general');
    this.writeToFile(filename, logEntry);

    console.error(`❌ ${context}:`, error.message);
  }

  logWarning(context, message, additionalInfo = {}) {
    if (!this.shouldLog('WARN')) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      type: 'WARNING',
      level: 'WARN',
      context: context,
      message: message,
      additionalInfo: additionalInfo
    };

    const filename = this.getLogFileName('general');
    this.writeToFile(filename, logEntry);

    console.warn(`⚠️  ${context}: ${message}`);
  }

  truncateContent(content, maxLength = 500) {
    if (!content || content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '... [TRUNCATED]';
  }

  // Utility method to get log statistics
  getLogStats(category, days = 7) {
    const stats = {
      totalEntries: 0,
      byType: {},
      byDay: {},
      fileSize: 0
    };

    try {
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const filename = this.getLogFileName(category, date);

        if (fs.existsSync(filename)) {
          const fileStats = fs.statSync(filename);
          stats.fileSize += fileStats.size;

          const content = fs.readFileSync(filename, 'utf8');
          const lines = content.trim().split('\n').filter(line => line.trim());

          const dayKey = date.toISOString().split('T')[0];
          stats.byDay[dayKey] = lines.length;
          stats.totalEntries += lines.length;

          lines.forEach(line => {
            try {
              const entry = JSON.parse(line);
              stats.byType[entry.type] = (stats.byType[entry.type] || 0) + 1;
            } catch (e) {
              // Skip invalid JSON lines
            }
          });
        }
      }
    } catch (error) {
      console.error('Error reading log stats:', error.message);
    }

    return stats;
  }
}

module.exports = new DocumentProcessingLogger();
