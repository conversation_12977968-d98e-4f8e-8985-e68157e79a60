#!/usr/bin/env node

/**
 * Test script for OpenRouter context optimization
 * Demonstrates improved context formatting and system prompt generation
 * Run with: node test_context_optimization.js
 */

// Mock the services for testing
const mockChunks = [
  {
    text: "Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed. It has revolutionized many industries including healthcare, finance, and technology.",
    score: 0.95,
    metadata: {
      filename: "ml_introduction.pdf",
      chunkIndex: 0,
      totalChunks: 5
    }
  },
  {
    text: "Supervised learning algorithms learn from labeled training data to make predictions on new, unseen data. Common examples include linear regression for predicting continuous values and classification algorithms for categorizing data.",
    score: 0.87,
    metadata: {
      filename: "ml_algorithms.pdf", 
      chunkIndex: 2,
      totalChunks: 8
    }
  },
  {
    text: "Neural networks are inspired by biological neural networks and consist of interconnected nodes that process information. Deep learning uses neural networks with multiple layers to model complex patterns in data.",
    score: 0.82,
    metadata: {
      filename: "deep_learning_guide.pdf",
      chunkIndex: 1,
      totalChunks: 6
    }
  },
  {
    text: "Data preprocessing is crucial for machine learning success. This includes cleaning data, handling missing values, feature scaling, and feature selection to improve model performance.",
    score: 0.75,
    metadata: {
      filename: "ml_introduction.pdf",
      chunkIndex: 3,
      totalChunks: 5
    }
  },
  {
    text: "Cross-validation is a technique used to assess how well a machine learning model will generalize to an independent dataset. It helps prevent overfitting and provides more reliable performance estimates.",
    score: 0.68,
    metadata: {
      filename: "ml_algorithms.pdf",
      chunkIndex: 5,
      totalChunks: 8
    }
  }
];

// Import the context formatting functions
function groupChunksByDocument(chunks) {
  const grouped = {};
  
  chunks.forEach(chunk => {
    const metadata = chunk.metadata || {};
    const filename = metadata.filename || 'Unknown Document';
    
    if (!grouped[filename]) {
      grouped[filename] = [];
    }
    grouped[filename].push(chunk);
  });
  
  return grouped;
}

function cleanDocumentName(filename) {
  return filename
    .replace(/\.[^/.]+$/, '') // Remove file extension
    .replace(/[-_]/g, ' ')    // Replace dashes/underscores with spaces
    .replace(/\b\w/g, l => l.toUpperCase()); // Title case
}

function combineRelatedChunks(chunks) {
  if (chunks.length === 1) {
    return chunks[0].text;
  }

  // Sort chunks by their original order in document (if available)
  const sortedChunks = chunks.sort((a, b) => {
    const aIndex = a.metadata?.chunkIndex || 0;
    const bIndex = b.metadata?.chunkIndex || 0;
    return aIndex - bIndex;
  });

  // Combine chunks with smart spacing
  return sortedChunks.map(chunk => {
    const text = chunk.text.trim();
    return text;
  }).join('\n\n');
}

function calculateAverageRelevance(chunks) {
  if (!chunks.length) return 0;
  const totalScore = chunks.reduce((sum, chunk) => sum + (chunk.score || 0), 0);
  return totalScore / chunks.length;
}

function assessContextQuality(chunks, context) {
  const avgRelevance = calculateAverageRelevance(chunks);
  const contextLength = context.length;
  
  if (avgRelevance > 0.8 && contextLength > 1000) return 'EXCELLENT';
  if (avgRelevance > 0.6 && contextLength > 500) return 'GOOD';
  if (avgRelevance > 0.4 && contextLength > 200) return 'FAIR';
  return 'POOR';
}

function formatOptimizedContext(chunks) {
  if (!chunks || chunks.length === 0) {
    return '';
  }

  // Sort chunks by relevance score (highest first)
  const sortedChunks = chunks.sort((a, b) => (b.score || 0) - (a.score || 0));
  
  // Group chunks by document for better context flow
  const chunksByDocument = groupChunksByDocument(sortedChunks);
  
  const contextParts = [];
  let totalLength = 0;
  const maxContextLength = 8000; // Optimal context length for most models

  for (const [documentName, documentChunks] of Object.entries(chunksByDocument)) {
    // Create document section header (clean, no technical details)
    const documentHeader = `=== ${cleanDocumentName(documentName)} ===`;
    
    // Combine related chunks from same document
    const combinedText = combineRelatedChunks(documentChunks);
    
    const sectionText = `${documentHeader}\n${combinedText}\n`;
    
    // Check if adding this section would exceed context limit
    if (totalLength + sectionText.length > maxContextLength) {
      console.log(`📏 Context length limit reached (${maxContextLength} chars), stopping at ${contextParts.length} sections`);
      break;
    }
    
    contextParts.push(sectionText);
    totalLength += sectionText.length;
  }

  return contextParts.join('\n');
}

function getQualityBasedInstructions(quality) {
  switch (quality) {
    case 'EXCELLENT':
      return `• I have high-quality, highly relevant information to answer your questions
• Provide comprehensive, detailed responses with confidence
• Include specific examples, data points, and nuanced explanations
• Feel free to elaborate on related topics that might be helpful`;

    case 'GOOD':
      return `• I have good information that should address your questions well
• Provide thorough responses while noting any limitations
• Focus on the most relevant and reliable information available
• Supplement with logical reasoning when appropriate`;

    case 'FAIR':
      return `• I have some relevant information, though it may be limited
• Provide what information is available while being transparent about gaps
• Focus on the most reliable aspects of the available information
• Suggest areas where additional clarification might be helpful`;

    case 'POOR':
      return `• My information on this topic appears limited
• Provide what relevant information is available
• Be transparent about limitations and uncertainty
• Suggest that the user might need to consult additional sources`;

    default:
      return `• Provide helpful responses based on available information
• Be clear about the scope and limitations of my knowledge
• Focus on accuracy and relevance in all responses`;
  }
}

function buildEnhancedSystemPrompt(context, contextMetadata = {}) {
  if (!context || context.trim() === '') {
    return `You are a knowledgeable AI assistant. I'm here to help answer your questions and provide insights. Please feel free to ask me anything, and I'll do my best to provide accurate and helpful information.`;
  }

  const contextQuality = contextMetadata.quality || 'GOOD';
  const qualityInstructions = getQualityBasedInstructions(contextQuality);

  return `You are an expert AI assistant with access to specialized knowledge. I can provide detailed, accurate answers based on my comprehensive information base.

KNOWLEDGE BASE:
${context}

RESPONSE GUIDELINES:
${qualityInstructions}

COMMUNICATION STYLE:
• Be conversational, professional, and engaging
• Provide specific details and examples when available
• Structure complex answers with clear organization
• Use natural language without referencing "documents" or "context"
• When uncertain, acknowledge limitations honestly
• Offer to clarify or expand on any topic

QUALITY STANDARDS:
• Prioritize accuracy over completeness
• Cite specific facts and figures when available
• Explain complex concepts in accessible terms
• Provide actionable insights when relevant
• Maintain consistency with the knowledge base

Remember: Respond as if this information is part of your trained knowledge, not external documents.`;
}

async function testContextOptimization() {
  console.log('🧪 Testing OpenRouter Context Optimization');
  console.log('==========================================\n');

  console.log('📊 Original chunks:');
  mockChunks.forEach((chunk, index) => {
    console.log(`   ${index + 1}. Score: ${chunk.score.toFixed(3)} | ${chunk.metadata.filename} | "${chunk.text.substring(0, 60)}..."`);
  });

  console.log('\n🔄 Applying context optimization...');

  // Test the optimized context formatting
  const optimizedContext = formatOptimizedContext(mockChunks);
  const avgRelevance = calculateAverageRelevance(mockChunks);
  const contextQuality = assessContextQuality(mockChunks, optimizedContext);

  console.log('\n📝 Optimized context formatting:');
  console.log('─'.repeat(80));
  console.log(optimizedContext);
  console.log('─'.repeat(80));

  console.log('\n📊 Context Quality Metrics:');
  console.log(`   📏 Length: ${optimizedContext.length} characters`);
  console.log(`   📄 Documents: ${Object.keys(groupChunksByDocument(mockChunks)).length}`);
  console.log(`   🧩 Chunks: ${mockChunks.length} total`);
  console.log(`   🎯 Avg relevance: ${avgRelevance.toFixed(3)}`);
  console.log(`   🏆 Quality assessment: ${contextQuality}`);

  // Test the enhanced system prompt
  const contextMetadata = {
    quality: contextQuality,
    chunkCount: mockChunks.length,
    avgRelevance: avgRelevance
  };

  const enhancedPrompt = buildEnhancedSystemPrompt(optimizedContext, contextMetadata);

  console.log('\n🤖 Enhanced System Prompt:');
  console.log('═'.repeat(80));
  console.log(enhancedPrompt);
  console.log('═'.repeat(80));

  console.log('\n🎉 Context Optimization Test Completed!');
  console.log('=======================================');
  console.log('✅ Context formatting: OPTIMIZED');
  console.log('✅ Document grouping: WORKING');
  console.log('✅ Quality assessment: WORKING');
  console.log('✅ Adaptive prompts: WORKING');
  console.log('✅ Relevance scoring: WORKING');
  
  console.log('\n💡 Key improvements:');
  console.log('   🧹 Clean document names (no technical details)');
  console.log('   📊 Relevance-based sorting');
  console.log('   📄 Document-based grouping');
  console.log('   🎯 Quality-adaptive instructions');
  console.log('   📏 Context length optimization');
  console.log('   🤖 Professional system prompts');
}

// Run test if this file is executed directly
if (require.main === module) {
  testContextOptimization().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testContextOptimization,
  formatOptimizedContext,
  buildEnhancedSystemPrompt
};
