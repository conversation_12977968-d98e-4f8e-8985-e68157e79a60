# Document Filtering Tools & Scripts

This document lists all the tools and scripts available for monitoring and analyzing document filtering.

## 🛠️ Available Tools

### 1. **test-logging.js** - Test the Logging System
```bash
node test-logging.js
```
- Tests all logging functionality
- Creates sample log entries
- Verifies log files are created correctly
- Shows sample JSON log structure

**Use when:** Setting up the system or troubleshooting logging issues

---

### 2. **demo-filtering.js** - See Filtering in Action
```bash
node demo-filtering.js
```
- Simulates a complete document processing pipeline
- Shows real-time console output with actual content examples
- Demonstrates page filtering, chunk filtering, and processing summaries
- Perfect for understanding what the output looks like

**Use when:** You want to see what filtering looks like without uploading a document

---

### 3. **analyze-logs.js** - Comprehensive Log Analysis
```bash
# Complete analysis
node analyze-logs.js

# Specific analysis
node analyze-logs.js pages 7        # Page filtering for 7 days
node analyze-logs.js chunks 3       # Chunk filtering for 3 days
node analyze-logs.js summary 1      # Processing summaries for 1 day
node analyze-logs.js examples chunk 10  # 10 recent chunk examples
```
- Analyzes filtering patterns and trends
- Shows statistics and breakdowns by filtering reason
- Displays recent examples with actual content
- Calculates performance metrics and cost savings

**Use when:** You want to understand filtering patterns and optimize criteria

---

## 📊 What Each Tool Shows

### Real-time Console Output (During Document Upload)
```
📄 Filtered page 3: Too short (3 < 20 words)
   📊 3 words, 25 chars
   📝 "Chapter 1  Introduction  "

🗑️  Filtered chunk 18: Copyright or legal notice
   📊 16 words, 95 chars
   📝 "© 2024 Research Institute. All rights re... [TRUNCATED]"

📊 Content Filtering Summary:
   📝 Characters filtered: 350
   📝 Words filtered: 58
   💾 Storage saved: ~0 KB of text
```

### Log Analysis Output
```
📄 PAGE FILTERING ANALYSIS
==========================
📊 Total entries: 15
📁 Log file size: 0.02 MB

🔍 Page filtering reasons:
   Too short: 8 pages
   Header-only content: 4 pages
   Footer-only content: 2 pages
   Low meaningful content: 1 pages

📄 Documents processed: 5
```

### Processing Performance
```
⚡ Processing performance:
   Documents processed: 5
   Average duration: 3200ms
   Total pages filtered: 12
   Total chunks filtered: 45
   Avg pages saved per doc: 2.4
   Avg chunks saved per doc: 9
```

---

## 📁 Log File Structure

```
logs/
├── document-processing/
│   ├── page-filtering/
│   │   └── 2025-07-15_page-filtering.log
│   ├── chunk-filtering/
│   │   └── 2025-07-15_chunk-filtering.log
│   └── processing-summary/
│       └── 2025-07-15_processing-summary.log
└── general/
    └── 2025-07-15_general.log
```

---

## 🔧 Configuration Options

### Environment Variables
```bash
# Enable/disable detailed filtering logs
ENABLE_DETAILED_FILTERING_LOGS=true

# Set log level (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# Maximum log file size before rotation (bytes)
MAX_LOG_FILE_SIZE=10485760
```

### What Gets Logged

#### Page Filtering
- ✅ Pages with too few words (< 20 words)
- ✅ Header-only pages (e.g., "Chapter 5")
- ✅ Footer-only pages (e.g., "Page 10 of 50")
- ✅ Pages with low meaningful content
- ✅ Page merging operations

#### Chunk Filtering
- ✅ Table formatting artifacts (dashes, borders)
- ✅ Navigation elements (page numbers, TOC)
- ✅ Copyright notices
- ✅ Very short chunks (< 100 characters)
- ✅ High repetition content
- ✅ Empty/whitespace-only content

---

## 🚀 Quick Start Workflow

### 1. **First Time Setup**
```bash
# Test the logging system
node test-logging.js

# See what filtering looks like
node demo-filtering.js
```

### 2. **Upload a Document**
Upload any document through your normal API and watch the console output

### 3. **Analyze the Results**
```bash
# See comprehensive analysis
node analyze-logs.js

# Focus on specific areas
node analyze-logs.js chunks 1    # Today's chunk filtering
node analyze-logs.js examples page 5  # Recent page examples
```

### 4. **Monitor Over Time**
```bash
# Weekly analysis
node analyze-logs.js pages 7
node analyze-logs.js chunks 7

# Real-time monitoring
tail -f logs/document-processing/chunk-filtering/$(date +%Y-%m-%d)_chunk-filtering.log
```

---

## 💡 Pro Tips

### Understanding Filtering Effectiveness
- **High page filtering (>30%)**: Document might have many structural pages
- **High chunk filtering (>25%)**: Document might have lots of tables/formatting
- **Low filtering (<5%)**: Document is already well-structured

### Optimizing Filtering Criteria
- Check `analyze-logs.js` output for patterns
- Look at actual content in filtered examples
- Adjust criteria in `src/routes/vectorProcessing.js` if needed

### Performance Monitoring
- Watch processing times in summaries
- Monitor cost savings percentages
- Track quality improvements over time

### Troubleshooting
- Use `test-logging.js` to verify logging works
- Check environment variables if no logs appear
- Use `demo-filtering.js` to see expected output format

---

## 📚 Related Documentation

- **[LOGGING_README.md](./LOGGING_README.md)** - Quick start guide
- **[FILTERING_LOGS_GUIDE.md](./FILTERING_LOGS_GUIDE.md)** - Complete technical guide
- **Environment setup** - See `.env.example` for configuration options

---

**Remember:** The goal is transparency and optimization. Use these tools to understand what's being filtered and why, then optimize your filtering criteria for better results!
