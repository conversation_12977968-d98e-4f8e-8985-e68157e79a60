# LlamaParse Retry Logic Implementation

## 🎯 Problem Solved

The system now handles network-related LlamaParse errors with intelligent retry logic, specifically addressing errors like:

```
❌ LlamaParse error for LLM24aug.pdf: request to https://api.cloud.llamaindex.ai/api/v1/parsing/upload failed, reason: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai
```

## 🔧 Implementation Overview

### **Multi-Level Retry Strategy**

1. **Main Parsing Retry**: Up to 3 attempts for the entire parsing process
2. **Upload Retry**: Integrated into main retry loop
3. **Status Check Retry**: Up to 2 additional retries for status polling
4. **Result Fetch Retry**: Up to 2 additional retries for result retrieval

### **Intelligent Error Classification**

The system automatically detects retryable vs non-retryable errors:

#### ✅ **Retryable Errors** (Network/Temporary Issues)
- `EAI_AGAIN` - DNS resolution failure
- `ENOTFOUND` - DNS lookup failed
- `ECONNRESET` - Connection reset
- `ECONNREFUSED` - Connection refused
- `ETIMEDOUT` - Request timeout
- `timeout` - General timeout
- `network` - Network error
- `fetch failed` - Fetch failure
- `socket hang up` - Socket error
- `getaddrinfo` - DNS resolution error
- `5xx server errors` - Temporary server issues

#### ❌ **Non-Retryable Errors** (Permanent Issues)
- `401 Unauthorized` - Authentication problems
- `400 Bad Request` - Invalid file format
- `403 Forbidden` - Permission issues
- `404 Not Found` - Endpoint not found
- `429 Too Many Requests` - Rate limiting (handled separately)

### **Smart Retry Delays**

- **Base delay**: 2 seconds
- **Exponential backoff**: 2s → 4s → 8s → 16s (capped at 30s)
- **Jitter**: Random 0-1s added to prevent thundering herd
- **Special handling**:
  - DNS errors: Minimum 5 seconds
  - Rate limits: Minimum 60 seconds

## 📊 Retry Configuration

```javascript
// Main parsing attempts
const maxRetries = 3;

// Status check retries
const statusMaxRetries = 2;

// Result fetch retries
const resultMaxRetries = 2;

// Delay calculation
baseDelay = 2000ms
maxDelay = 30000ms
dnsMinDelay = 5000ms
rateLimitMinDelay = 60000ms
```

## 🚀 Usage Examples

### **Successful Retry Scenario**
```
📄 Starting to parse file: document.pdf
❌ Attempt 1 failed: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai
🔄 Retrying in 5.2 seconds... (2 attempts remaining)
❌ Attempt 2 failed: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai  
🔄 Retrying in 10.8 seconds... (1 attempts remaining)
✅ Attempt 3 succeeded: File uploaded successfully
🔄 File uploaded, job ID: abc123. Waiting for processing...
✅ Parsing completed for job abc123
```

### **Non-Retryable Error Scenario**
```
📄 Starting to parse file: document.pdf
❌ LlamaParse error for document.pdf after 1 attempts: Upload failed: 401 - Unauthorized
```

### **Max Retries Exceeded**
```
📄 Starting to parse file: document.pdf
❌ Attempt 1 failed: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai
🔄 Retrying in 5.2 seconds... (2 attempts remaining)
❌ Attempt 2 failed: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai
🔄 Retrying in 10.8 seconds... (1 attempts remaining)
❌ Attempt 3 failed: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai
❌ LlamaParse error for document.pdf after 3 attempts: getaddrinfo EAI_AGAIN api.cloud.llamaindex.ai
```

## 🔍 Monitoring & Debugging

### **Console Output**
The retry logic provides detailed console output:
- Attempt numbers and remaining retries
- Specific error messages
- Retry delays
- Success/failure status

### **Error Patterns to Watch**
- Frequent DNS errors may indicate network configuration issues
- Consistent timeouts may suggest server overload
- Authentication errors require API key verification

## 🧪 Testing

Run the retry logic test:
```bash
node test_llamaparse_retry.js
```

This test verifies:
- ✅ Error classification accuracy (100% success rate)
- ✅ Retry delay calculation
- ✅ Configuration parameters
- ✅ Retryable error patterns

## 💡 Benefits

1. **Improved Reliability**: Handles temporary network issues automatically
2. **Better User Experience**: Reduces failed document uploads
3. **Smart Resource Usage**: Avoids unnecessary retries for permanent errors
4. **Configurable**: Easy to adjust retry counts and delays
5. **Observable**: Clear logging for debugging and monitoring

## 🔧 Configuration Options

The retry behavior can be customized by modifying the constants in `llamaParseService.js`:

```javascript
// In parseFile method
const maxRetries = 3; // Adjust main retry count

// In getParseStatusWithRetry method  
const maxRetries = 2; // Adjust status check retries

// In getParseResultWithRetry method
const maxRetries = 2; // Adjust result fetch retries

// In calculateRetryDelay method
const baseDelay = 2000; // Base delay in milliseconds
const maxDelay = 30000; // Maximum delay cap
```

## 🚨 Important Notes

- Rate limit errors (429) are not automatically retried to avoid making the situation worse
- The system respects exponential backoff to be kind to the LlamaParse API
- All retries include jitter to prevent synchronized retry storms
- DNS errors get special treatment with longer minimum delays
- The retry logic is applied at multiple levels for comprehensive coverage
